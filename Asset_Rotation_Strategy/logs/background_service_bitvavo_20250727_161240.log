2025-07-27 16:12:40,360 - [<PERSON><PERSON><PERSON><PERSON>] - root - INFO - Exchange-specific logging initialized for bitvavo
2025-07-27 16:12:40,640 - [BITVA<PERSON>] - root - INFO - Telegram command handlers registered
2025-07-27 16:12:40,641 - [B<PERSON><PERSON><PERSON>] - root - INFO - Telegram bot polling started
2025-07-27 16:12:40,641 - [B<PERSON><PERSON><PERSON>] - root - INFO - Telegram notifier initialized with notification level: standard
2025-07-27 16:12:40,641 - [B<PERSON><PERSON><PERSON>] - root - INFO - Telegram notification channel initialized
2025-07-27 16:12:40,642 - [BIT<PERSON><PERSON>] - root - ERROR - Error in Telegram polling: set_wakeup_fd only works in main thread of the main interpreter
2025-07-27 16:12:40,642 - [B<PERSON><PERSON><PERSON>] - root - WARNING - Continuing without Telegram bot polling. Notifications will still be sent.
2025-07-27 16:12:40,643 - [<PERSON><PERSON>VA<PERSON>] - root - INFO - Successfully loaded templates using utf-8 encoding
2025-07-27 16:12:40,644 - [<PERSON><PERSON><PERSON><PERSON>] - root - INFO - <PERSON>aded 26 templates from file
2025-07-27 16:12:40,644 - [<PERSON><PERSON><PERSON><PERSON>] - root - INFO - Notification manager initialized with 1 channels
2025-07-27 16:12:40,644 - [BITVAVO] - root - INFO - Notification manager initialized
2025-07-27 16:12:40,644 - [BITVAVO] - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-07-27 16:12:40,645 - [BITVAVO] - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-07-27 16:12:40,645 - [BITVAVO] - root - INFO - Set up critical time windows for 1d timeframe
2025-07-27 16:12:40,645 - [BITVAVO] - root - INFO - Network watchdog initialized with 10s check interval
2025-07-27 16:12:40,647 - [BITVAVO] - root - INFO - Loaded recovery state from data/state/recovery_state.json
2025-07-27 16:12:40,649 - [BITVAVO] - root - INFO - No state file found at /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/data/state/data/state/background_service_20250627_214259.json.json
2025-07-27 16:12:40,649 - [BITVAVO] - root - INFO - Recovery manager initialized
2025-07-27 16:12:40,649 - [BITVAVO] - root - INFO - [DEBUG] TRADING EXECUTOR - Initializing with exchange: bitvavo
2025-07-27 16:12:40,650 - [BITVAVO] - root - INFO - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'bitvavo', 'exchange_params': {'bitvavo': {'operator_id': 1001}}, 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'default': 5.0}, 'mode': 'paper', 'order_type': 'market', 'position_size_pct': 99.99, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 20}, 'transaction_fee_rate': 0.0025}
2025-07-27 16:12:40,650 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-27 16:12:40,666 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-27 16:12:40,666 - [BITVAVO] - root - INFO - Getting credentials for exchange_id: bitvavo
2025-07-27 16:12:40,666 - [BITVAVO] - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-07-27 16:12:40,666 - [BITVAVO] - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-07-27 16:12:40,666 - [BITVAVO] - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-07-27 16:12:40,666 - [BITVAVO] - root - INFO - Getting credentials for exchange_id: bitvavo
2025-07-27 16:12:40,666 - [BITVAVO] - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-07-27 16:12:40,666 - [BITVAVO] - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-07-27 16:12:40,666 - [BITVAVO] - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-07-27 16:12:40,666 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-27 16:12:40,680 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-27 16:12:41,153 - [BITVAVO] - root - INFO - Successfully loaded markets for bitvavo.
2025-07-27 16:12:41,153 - [BITVAVO] - root - INFO - Getting credentials for exchange_id: bitvavo
2025-07-27 16:12:41,154 - [BITVAVO] - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-07-27 16:12:41,154 - [BITVAVO] - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-07-27 16:12:41,154 - [BITVAVO] - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-07-27 16:12:41,154 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-27 16:12:41,165 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-27 16:12:41,169 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-27 16:12:41,181 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-27 16:12:41,181 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-27 16:12:41,192 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-27 16:12:41,193 - [BITVAVO] - root - INFO - Loaded paper trading history from paper_trading_history.json
2025-07-27 16:12:41,193 - [BITVAVO] - root - INFO - Paper trading initialized with EUR as quote currency
2025-07-27 16:12:41,193 - [BITVAVO] - root - INFO - Trading executor initialized for bitvavo
2025-07-27 16:12:41,193 - [BITVAVO] - root - INFO - Trading mode: paper
2025-07-27 16:12:41,193 - [BITVAVO] - root - INFO - Trading enabled: True
2025-07-27 16:12:41,193 - [BITVAVO] - root - INFO - Getting credentials for exchange_id: bitvavo
2025-07-27 16:12:41,193 - [BITVAVO] - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-07-27 16:12:41,194 - [BITVAVO] - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-07-27 16:12:41,194 - [BITVAVO] - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-07-27 16:12:41,194 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-27 16:12:41,204 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-27 16:12:41,690 - [BITVAVO] - root - INFO - Successfully loaded markets for bitvavo.
2025-07-27 16:12:41,690 - [BITVAVO] - root - INFO - Notification manager passed to trading executor
2025-07-27 16:12:41,690 - [BITVAVO] - root - INFO - Trading enabled in paper mode
2025-07-27 16:12:41,690 - [BITVAVO] - root - INFO - Saved paper trading history to paper_trading_history.json
2025-07-27 16:12:41,690 - [BITVAVO] - root - INFO - Reset paper trading account to initial balance: 100 EUR
2025-07-27 16:12:41,691 - [BITVAVO] - root - INFO - Reset paper trading account to initial balance
2025-07-27 16:12:41,691 - [BITVAVO] - root - INFO - Generated run ID: 20250727_161241
2025-07-27 16:12:41,691 - [BITVAVO] - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-07-27 16:12:41,691 - [BITVAVO] - root - INFO - Background service initialized
2025-07-27 16:12:41,691 - [BITVAVO] - root - INFO - Network watchdog started
2025-07-27 16:12:41,692 - [BITVAVO] - root - INFO - Network watchdog started
2025-07-27 16:12:41,692 - [BITVAVO] - root - INFO - Schedule set up for 1d timeframe
2025-07-27 16:12:41,692 - [BITVAVO] - root - INFO - Background service started
2025-07-27 16:12:41,693 - [BITVAVO] - root - INFO - Executing strategy (run #1)...
2025-07-27 16:12:41,695 - [BITVAVO] - root - INFO - Resetting daily trade counters for this strategy execution
2025-07-27 16:12:41,696 - [BITVAVO] - root - INFO - No trades recorded today (Max: 5)
2025-07-27 16:12:41,697 - [BITVAVO] - root - INFO - Initialized daily trades counter for 2025-07-27
2025-07-27 16:12:41,703 - [BITVAVO] - root - INFO - Creating snapshot for candle timestamp: 20250727
2025-07-27 16:12:41,792 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-27 16:12:42,710 - [BITVAVO] - root - WARNING - Failed to send with Markdown formatting: Unknown error in HTTP implementation: RuntimeError('<asyncio.locks.Event object at 0x717c2c25d850 [unset]> is bound to a different event loop')
2025-07-27 16:12:42,776 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-27 16:12:42,779 - [BITVAVO] - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-07-27 16:12:42,779 - [BITVAVO] - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-07-27 16:12:42,779 - [BITVAVO] - root - INFO - Using recent date for performance tracking: 2025-07-20
2025-07-27 16:12:42,781 - [BITVAVO] - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-07-27 16:12:42,827 - [BITVAVO] - root - INFO - Loaded 2168 rows of ETH/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:42,828 - [BITVAVO] - root - INFO - Last timestamp in cache for ETH/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:42,828 - [BITVAVO] - root - INFO - Expected last timestamp for ETH/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:42,828 - [BITVAVO] - root - INFO - Data is up to date for ETH/USDT
2025-07-27 16:12:42,829 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:42,845 - [BITVAVO] - root - INFO - Loaded 2168 rows of BTC/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:42,846 - [BITVAVO] - root - INFO - Last timestamp in cache for BTC/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:42,846 - [BITVAVO] - root - INFO - Expected last timestamp for BTC/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:42,846 - [BITVAVO] - root - INFO - Data is up to date for BTC/USDT
2025-07-27 16:12:42,847 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:42,868 - [BITVAVO] - root - INFO - Loaded 1811 rows of SOL/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:42,869 - [BITVAVO] - root - INFO - Last timestamp in cache for SOL/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:42,869 - [BITVAVO] - root - INFO - Expected last timestamp for SOL/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:42,871 - [BITVAVO] - root - INFO - Data is up to date for SOL/USDT
2025-07-27 16:12:42,872 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:42,887 - [BITVAVO] - root - INFO - Loaded 816 rows of SUI/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:42,888 - [BITVAVO] - root - INFO - Last timestamp in cache for SUI/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:42,889 - [BITVAVO] - root - INFO - Expected last timestamp for SUI/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:42,889 - [BITVAVO] - root - INFO - Data is up to date for SUI/USDT
2025-07-27 16:12:42,890 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:42,907 - [BITVAVO] - root - INFO - Loaded 2168 rows of XRP/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:42,908 - [BITVAVO] - root - INFO - Last timestamp in cache for XRP/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:42,908 - [BITVAVO] - root - INFO - Expected last timestamp for XRP/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:42,909 - [BITVAVO] - root - INFO - Data is up to date for XRP/USDT
2025-07-27 16:12:42,910 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:42,939 - [BITVAVO] - root - INFO - Loaded 1746 rows of AAVE/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:42,939 - [BITVAVO] - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:42,940 - [BITVAVO] - root - INFO - Expected last timestamp for AAVE/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:42,940 - [BITVAVO] - root - INFO - Data is up to date for AAVE/USDT
2025-07-27 16:12:42,941 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:42,955 - [BITVAVO] - root - INFO - Loaded 1769 rows of AVAX/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:42,955 - [BITVAVO] - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:42,955 - [BITVAVO] - root - INFO - Expected last timestamp for AVAX/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:42,956 - [BITVAVO] - root - INFO - Data is up to date for AVAX/USDT
2025-07-27 16:12:42,956 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:42,974 - [BITVAVO] - root - INFO - Loaded 2168 rows of ADA/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:42,974 - [BITVAVO] - root - INFO - Last timestamp in cache for ADA/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:42,974 - [BITVAVO] - root - INFO - Expected last timestamp for ADA/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:42,974 - [BITVAVO] - root - INFO - Data is up to date for ADA/USDT
2025-07-27 16:12:42,975 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:43,006 - [BITVAVO] - root - INFO - Loaded 2168 rows of LINK/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:43,007 - [BITVAVO] - root - INFO - Last timestamp in cache for LINK/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:43,007 - [BITVAVO] - root - INFO - Expected last timestamp for LINK/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:43,007 - [BITVAVO] - root - INFO - Data is up to date for LINK/USDT
2025-07-27 16:12:43,009 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:43,027 - [BITVAVO] - root - INFO - Loaded 2168 rows of TRX/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:43,028 - [BITVAVO] - root - INFO - Last timestamp in cache for TRX/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:43,028 - [BITVAVO] - root - INFO - Expected last timestamp for TRX/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:43,028 - [BITVAVO] - root - INFO - Data is up to date for TRX/USDT
2025-07-27 16:12:43,029 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:43,039 - [BITVAVO] - root - INFO - Loaded 814 rows of PEPE/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:43,040 - [BITVAVO] - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:43,040 - [BITVAVO] - root - INFO - Expected last timestamp for PEPE/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:43,040 - [BITVAVO] - root - INFO - Data is up to date for PEPE/USDT
2025-07-27 16:12:43,041 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:43,060 - [BITVAVO] - root - INFO - Loaded 2168 rows of DOGE/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:43,060 - [BITVAVO] - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:43,060 - [BITVAVO] - root - INFO - Expected last timestamp for DOGE/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:43,061 - [BITVAVO] - root - INFO - Data is up to date for DOGE/USDT
2025-07-27 16:12:43,062 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:43,078 - [BITVAVO] - root - INFO - Loaded 2168 rows of BNB/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:43,078 - [BITVAVO] - root - INFO - Last timestamp in cache for BNB/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:43,079 - [BITVAVO] - root - INFO - Expected last timestamp for BNB/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:43,079 - [BITVAVO] - root - INFO - Data is up to date for BNB/USDT
2025-07-27 16:12:43,080 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:43,096 - [BITVAVO] - root - INFO - Loaded 1804 rows of DOT/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:43,096 - [BITVAVO] - root - INFO - Last timestamp in cache for DOT/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:43,097 - [BITVAVO] - root - INFO - Expected last timestamp for DOT/USDT: 2025-07-26 00:00:00+00:00
2025-07-27 16:12:43,097 - [BITVAVO] - root - INFO - Data is up to date for DOT/USDT
2025-07-27 16:12:43,098 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:43,099 - [BITVAVO] - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-07-27 16:12:43,099 - [BITVAVO] - root - INFO - MTPI Multi-Indicator Configuration:
2025-07-27 16:12:43,100 - [BITVAVO] - root - INFO -   - Number of indicators: 8
2025-07-27 16:12:43,100 - [BITVAVO] - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-27 16:12:43,100 - [BITVAVO] - root - INFO -   - Combination method: consensus
2025-07-27 16:12:43,100 - [BITVAVO] - root - INFO -   - Long threshold: 0.1
2025-07-27 16:12:43,100 - [BITVAVO] - root - INFO -   - Short threshold: -0.1
2025-07-27 16:12:43,100 - [BITVAVO] - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-27 16:12:43,100 - [BITVAVO] - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-27 16:12:43,100 - [BITVAVO] - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: 2
2025-07-27 16:12:43,100 - [BITVAVO] - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 10
2025-07-27 16:12:43,100 - [BITVAVO] - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-07-27 16:12:43,100 - [BITVAVO] - root - INFO - Parameters: use_mtpi_signal=True, mtpi_indicator_type=PGO
2025-07-27 16:12:43,100 - [BITVAVO] - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-07-27 16:12:43,101 - [BITVAVO] - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-07-27 16:12:43,101 - [BITVAVO] - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-07-27 16:12:43,101 - [BITVAVO] - root - INFO - n_assets=2, use_weighted_allocation=True
2025-07-27 16:12:43,101 - [BITVAVO] - root - INFO - Using provided trend method: PGO For Loop
2025-07-27 16:12:43,101 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-27 16:12:43,114 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-27 16:12:43,114 - [BITVAVO] - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-07-27 16:12:43,121 - [BITVAVO] - root - INFO - Configuration saved successfully.
2025-07-27 16:12:43,121 - [BITVAVO] - root - INFO - Weighted allocation enabled with weights: [0.5, 0.5]
2025-07-27 16:12:43,121 - [BITVAVO] - root - INFO - Weights type: <class 'list'>, values: [0.5, 0.5]
2025-07-27 16:12:43,121 - [BITVAVO] - root - INFO - Weights sum: 1.0
2025-07-27 16:12:43,121 - [BITVAVO] - root - INFO - Weights are already normalized (sum to 1.0)
2025-07-27 16:12:43,121 - [BITVAVO] - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-27 16:12:43,121 - [BITVAVO] - root - INFO - Number of trend detection assets: 14
2025-07-27 16:12:43,121 - [BITVAVO] - root - INFO - Selected assets type: <class 'list'>
2025-07-27 16:12:43,122 - [BITVAVO] - root - INFO - Trading execution assets (EUR): ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-27 16:12:43,122 - [BITVAVO] - root - INFO - Number of trading assets: 14
2025-07-27 16:12:43,122 - [BITVAVO] - root - INFO - Trading assets type: <class 'list'>
2025-07-27 16:12:43,297 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-27 16:12:43,311 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-27 16:12:43,326 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-27 16:12:43,338 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-27 16:12:43,339 - [BITVAVO] - root - INFO - Execution context: backtesting
2025-07-27 16:12:43,339 - [BITVAVO] - root - INFO - Execution timing: candle_close
2025-07-27 16:12:43,339 - [BITVAVO] - root - INFO - Ratio calculation method: independent
2025-07-27 16:12:43,339 - [BITVAVO] - root - INFO - Tie-breaking strategy: imcumbent
2025-07-27 16:12:43,339 - [BITVAVO] - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-07-27 16:12:43,339 - [BITVAVO] - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-27 16:12:43,339 - [BITVAVO] - root - INFO - MTPI combination method override: consensus
2025-07-27 16:12:43,339 - [BITVAVO] - root - INFO - MTPI long threshold override: 0.1
2025-07-27 16:12:43,339 - [BITVAVO] - root - INFO - MTPI short threshold override: -0.1
2025-07-27 16:12:43,339 - [BITVAVO] - root - INFO - Using extended warmup period of 120 days for weighted allocation with 1d timeframe
2025-07-27 16:12:43,339 - [BITVAVO] - root - INFO - Fetching data from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-07-27 16:12:43,341 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-27 16:12:43,341 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-27 16:12:43,341 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-27 16:12:43,341 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-27 16:12:43,342 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-27 16:12:43,342 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-27 16:12:43,342 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-27 16:12:43,342 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-27 16:12:43,342 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-27 16:12:43,342 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-27 16:12:43,343 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-27 16:12:43,343 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-27 16:12:43,343 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-27 16:12:43,343 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-27 16:12:43,343 - [BITVAVO] - root - INFO - Checking cache for 14 symbols (1d)...
2025-07-27 16:12:43,363 - [BITVAVO] - root - INFO - Loaded 287 rows of ETH/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:43,364 - [BITVAVO] - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-10-13. Using cached data.
2025-07-27 16:12:43,365 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:43,365 - [BITVAVO] - root - INFO - Loaded 287 rows of ETH/USDT data from cache (after filtering).
2025-07-27 16:12:43,389 - [BITVAVO] - root - INFO - Loaded 287 rows of BTC/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:43,391 - [BITVAVO] - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-10-13. Using cached data.
2025-07-27 16:12:43,391 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:43,391 - [BITVAVO] - root - INFO - Loaded 287 rows of BTC/USDT data from cache (after filtering).
2025-07-27 16:12:43,407 - [BITVAVO] - root - INFO - Loaded 287 rows of SOL/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:43,409 - [BITVAVO] - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-10-13. Using cached data.
2025-07-27 16:12:43,410 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:43,410 - [BITVAVO] - root - INFO - Loaded 287 rows of SOL/USDT data from cache (after filtering).
2025-07-27 16:12:43,419 - [BITVAVO] - root - INFO - Loaded 287 rows of SUI/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:43,420 - [BITVAVO] - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-10-13. Using cached data.
2025-07-27 16:12:43,421 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:43,421 - [BITVAVO] - root - INFO - Loaded 287 rows of SUI/USDT data from cache (after filtering).
2025-07-27 16:12:43,441 - [BITVAVO] - root - INFO - Loaded 287 rows of XRP/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:43,442 - [BITVAVO] - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-10-13. Using cached data.
2025-07-27 16:12:43,442 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:43,442 - [BITVAVO] - root - INFO - Loaded 287 rows of XRP/USDT data from cache (after filtering).
2025-07-27 16:12:43,456 - [BITVAVO] - root - INFO - Loaded 287 rows of AAVE/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:43,457 - [BITVAVO] - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-10-13. Using cached data.
2025-07-27 16:12:43,457 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:43,457 - [BITVAVO] - root - INFO - Loaded 287 rows of AAVE/USDT data from cache (after filtering).
2025-07-27 16:12:43,472 - [BITVAVO] - root - INFO - Loaded 287 rows of AVAX/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:43,473 - [BITVAVO] - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-10-13. Using cached data.
2025-07-27 16:12:43,474 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:43,474 - [BITVAVO] - root - INFO - Loaded 287 rows of AVAX/USDT data from cache (after filtering).
2025-07-27 16:12:43,492 - [BITVAVO] - root - INFO - Loaded 287 rows of ADA/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:43,493 - [BITVAVO] - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-10-13. Using cached data.
2025-07-27 16:12:43,493 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:43,493 - [BITVAVO] - root - INFO - Loaded 287 rows of ADA/USDT data from cache (after filtering).
2025-07-27 16:12:43,512 - [BITVAVO] - root - INFO - Loaded 287 rows of LINK/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:43,513 - [BITVAVO] - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-10-13. Using cached data.
2025-07-27 16:12:43,514 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:43,514 - [BITVAVO] - root - INFO - Loaded 287 rows of LINK/USDT data from cache (after filtering).
2025-07-27 16:12:43,533 - [BITVAVO] - root - INFO - Loaded 287 rows of TRX/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:43,534 - [BITVAVO] - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-10-13. Using cached data.
2025-07-27 16:12:43,535 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:43,535 - [BITVAVO] - root - INFO - Loaded 287 rows of TRX/USDT data from cache (after filtering).
2025-07-27 16:12:43,544 - [BITVAVO] - root - INFO - Loaded 287 rows of PEPE/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:43,545 - [BITVAVO] - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-10-13. Using cached data.
2025-07-27 16:12:43,546 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:43,546 - [BITVAVO] - root - INFO - Loaded 287 rows of PEPE/USDT data from cache (after filtering).
2025-07-27 16:12:43,564 - [BITVAVO] - root - INFO - Loaded 287 rows of DOGE/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:43,565 - [BITVAVO] - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-10-13. Using cached data.
2025-07-27 16:12:43,566 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:43,566 - [BITVAVO] - root - INFO - Loaded 287 rows of DOGE/USDT data from cache (after filtering).
2025-07-27 16:12:43,583 - [BITVAVO] - root - INFO - Loaded 287 rows of BNB/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:43,584 - [BITVAVO] - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-10-13. Using cached data.
2025-07-27 16:12:43,584 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:43,584 - [BITVAVO] - root - INFO - Loaded 287 rows of BNB/USDT data from cache (after filtering).
2025-07-27 16:12:43,600 - [BITVAVO] - root - INFO - Loaded 287 rows of DOT/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:43,601 - [BITVAVO] - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-10-13. Using cached data.
2025-07-27 16:12:43,601 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:43,601 - [BITVAVO] - root - INFO - Loaded 287 rows of DOT/USDT data from cache (after filtering).
2025-07-27 16:12:43,602 - [BITVAVO] - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-27 16:12:43,602 - [BITVAVO] - root - INFO - Asset ETH/USDT first available date: 2024-10-13 00:00:00+00:00
2025-07-27 16:12:43,602 - [BITVAVO] - root - INFO - Asset BTC/USDT first available date: 2024-10-13 00:00:00+00:00
2025-07-27 16:12:43,602 - [BITVAVO] - root - INFO - Asset SOL/USDT first available date: 2024-10-13 00:00:00+00:00
2025-07-27 16:12:43,602 - [BITVAVO] - root - INFO - Asset SUI/USDT first available date: 2024-10-13 00:00:00+00:00
2025-07-27 16:12:43,602 - [BITVAVO] - root - INFO - Asset XRP/USDT first available date: 2024-10-13 00:00:00+00:00
2025-07-27 16:12:43,602 - [BITVAVO] - root - INFO - Asset AAVE/USDT first available date: 2024-10-13 00:00:00+00:00
2025-07-27 16:12:43,603 - [BITVAVO] - root - INFO - Asset AVAX/USDT first available date: 2024-10-13 00:00:00+00:00
2025-07-27 16:12:43,603 - [BITVAVO] - root - INFO - Asset ADA/USDT first available date: 2024-10-13 00:00:00+00:00
2025-07-27 16:12:43,603 - [BITVAVO] - root - INFO - Asset LINK/USDT first available date: 2024-10-13 00:00:00+00:00
2025-07-27 16:12:43,603 - [BITVAVO] - root - INFO - Asset TRX/USDT first available date: 2024-10-13 00:00:00+00:00
2025-07-27 16:12:43,603 - [BITVAVO] - root - INFO - Asset PEPE/USDT first available date: 2024-10-13 00:00:00+00:00
2025-07-27 16:12:43,603 - [BITVAVO] - root - INFO - Asset DOGE/USDT first available date: 2024-10-13 00:00:00+00:00
2025-07-27 16:12:43,604 - [BITVAVO] - root - INFO - Asset BNB/USDT first available date: 2024-10-13 00:00:00+00:00
2025-07-27 16:12:43,604 - [BITVAVO] - root - INFO - Asset DOT/USDT first available date: 2024-10-13 00:00:00+00:00
2025-07-27 16:12:43,634 - [BITVAVO] - root - INFO - Using extended MTPI warmup period of 120 days for weighted allocation with 1d timeframe
2025-07-27 16:12:43,635 - [BITVAVO] - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-07-27 16:12:43,635 - [BITVAVO] - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-07-27 16:12:43,635 - [BITVAVO] - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-07-27 16:12:43,635 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-27 16:12:43,647 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-27 16:12:43,647 - [BITVAVO] - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-07-27 16:12:43,647 - [BITVAVO] - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-07-27 16:12:43,647 - [BITVAVO] - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-27 16:12:43,647 - [BITVAVO] - root - INFO - Override: combination_method = consensus
2025-07-27 16:12:43,648 - [BITVAVO] - root - INFO - Override: long_threshold = 0.1
2025-07-27 16:12:43,648 - [BITVAVO] - root - INFO - Override: short_threshold = -0.1
2025-07-27 16:12:43,648 - [BITVAVO] - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-07-27 16:12:43,648 - [BITVAVO] - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-07-27 16:12:43,648 - [BITVAVO] - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-07-27 16:12:43,648 - [BITVAVO] - root - INFO - Checking cache for 1 symbols (1d)...
2025-07-27 16:12:43,681 - [BITVAVO] - root - INFO - Loaded 287 rows of BTC/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:12:43,681 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:12:43,681 - [BITVAVO] - root - INFO - Loaded 287 rows of BTC/USDT data from cache (after filtering).
2025-07-27 16:12:43,682 - [BITVAVO] - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-27 16:12:43,682 - [BITVAVO] - root - INFO - Fetched BTC data: 287 candles from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 16:12:43,682 - [BITVAVO] - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-07-27 16:12:43,741 - [BITVAVO] - root - INFO - Generated PGO Score signals: {-1: np.int64(111), 0: np.int64(34), 1: np.int64(142)}
2025-07-27 16:12:43,741 - [BITVAVO] - root - INFO - Generated pgo signals: 287 values
2025-07-27 16:12:43,741 - [BITVAVO] - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-07-27 16:12:43,741 - [BITVAVO] - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-07-27 16:12:43,759 - [BITVAVO] - root - INFO - Generated BB Score signals: {-1: np.int64(109), 0: np.int64(32), 1: np.int64(146)}
2025-07-27 16:12:43,760 - [BITVAVO] - root - INFO - Generated Bollinger Band signals: 287 values
2025-07-27 16:12:43,760 - [BITVAVO] - root - INFO - Generated bollinger_bands signals: 287 values
2025-07-27 16:12:44,430 - [BITVAVO] - root - INFO - Generated DWMA signals using Weighted SD method
2025-07-27 16:12:44,430 - [BITVAVO] - root - INFO - Generated dwma_score signals: 287 values
2025-07-27 16:12:44,514 - [BITVAVO] - root - INFO - Generated DEMA Supertrend signals
2025-07-27 16:12:44,515 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(156), 0: np.int64(1), 1: np.int64(130)}
2025-07-27 16:12:44,515 - [BITVAVO] - root - INFO - Generated DEMA Super Score signals
2025-07-27 16:12:44,515 - [BITVAVO] - root - INFO - Generated dema_super_score signals: 287 values
2025-07-27 16:12:44,696 - [BITVAVO] - root - INFO - Generated DPSD signals
2025-07-27 16:12:44,696 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(105), 0: np.int64(87), 1: np.int64(95)}
2025-07-27 16:12:44,696 - [BITVAVO] - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-07-27 16:12:44,696 - [BITVAVO] - root - INFO - Generated dpsd_score signals: 287 values
2025-07-27 16:12:44,710 - [BITVAVO] - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-07-27 16:12:44,710 - [BITVAVO] - root - INFO - Generated AAD Score signals using SMA method
2025-07-27 16:12:44,711 - [BITVAVO] - root - INFO - Generated aad_score signals: 287 values
2025-07-27 16:12:44,830 - [BITVAVO] - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-07-27 16:12:44,831 - [BITVAVO] - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-07-27 16:12:44,831 - [BITVAVO] - root - INFO - Generated dynamic_ema_score signals: 287 values
2025-07-27 16:12:45,063 - [BITVAVO] - root - INFO - Generated quantile_dema_score signals: 287 values
2025-07-27 16:12:45,078 - [BITVAVO] - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-07-27 16:12:45,079 - [BITVAVO] - root - INFO - Signal distribution: {1: 162, -1: 124, 0: 1}
2025-07-27 16:12:45,080 - [BITVAVO] - root - INFO - Generated combined MTPI signals: 287 values using consensus method
2025-07-27 16:12:45,080 - [BITVAVO] - root - INFO - Signal distribution: {1: 162, -1: 124, 0: 1}
2025-07-27 16:12:45,081 - [BITVAVO] - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-07-27 16:12:45,085 - [BITVAVO] - root - INFO - Saving configuration to config/settings.yaml...
2025-07-27 16:12:45,093 - [BITVAVO] - root - INFO - Configuration saved successfully.
2025-07-27 16:12:45,093 - [BITVAVO] - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-07-27 16:12:45,093 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-27 16:12:45,105 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-27 16:12:45,105 - [BITVAVO] - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-07-27 16:12:45,105 - [BITVAVO] - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-07-27 16:12:45,105 - [BITVAVO] - root - INFO - Using ratio calculation method: independent
2025-07-27 16:12:45,147 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:45,147 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:45,185 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:45,199 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data
2025-07-27 16:12:45,246 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-27 16:12:45,246 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:45,284 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-27 16:12:45,299 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-07-27 16:12:45,354 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-27 16:12:45,354 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:45,408 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-27 16:12:45,421 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-07-27 16:12:45,468 - [BITVAVO] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-27 16:12:45,469 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:45,508 - [BITVAVO] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-27 16:12:45,521 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-07-27 16:12:45,569 - [BITVAVO] - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-07-27 16:12:45,570 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:45,611 - [BITVAVO] - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-07-27 16:12:45,624 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-07-27 16:12:45,676 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:45,677 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:45,718 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:45,731 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-07-27 16:12:45,776 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-27 16:12:45,776 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:45,814 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-27 16:12:45,827 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-07-27 16:12:45,867 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-27 16:12:45,867 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:45,905 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-27 16:12:45,917 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-07-27 16:12:45,957 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:45,957 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:45,994 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:46,007 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-07-27 16:12:46,047 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-27 16:12:46,048 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:46,112 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-27 16:12:46,127 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-07-27 16:12:46,181 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-27 16:12:46,182 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:46,221 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-27 16:12:46,234 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-07-27 16:12:46,276 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-27 16:12:46,276 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:46,312 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-27 16:12:46,325 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-07-27 16:12:46,364 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-27 16:12:46,364 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:46,402 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-27 16:12:46,415 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-07-27 16:12:46,456 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:46,504 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ETH/USDT using full OHLCV data
2025-07-27 16:12:46,545 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:46,594 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data
2025-07-27 16:12:46,640 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-27 16:12:46,640 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:46,681 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-27 16:12:46,697 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data
2025-07-27 16:12:46,737 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-27 16:12:46,737 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:46,776 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-27 16:12:46,789 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data
2025-07-27 16:12:46,830 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:46,878 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data
2025-07-27 16:12:46,917 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:46,967 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data
2025-07-27 16:12:47,005 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-27 16:12:47,006 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:47,040 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-27 16:12:47,060 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data
2025-07-27 16:12:47,100 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:47,100 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:47,138 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:47,150 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data
2025-07-27 16:12:47,190 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:47,190 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:47,225 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:47,237 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data
2025-07-27 16:12:47,276 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-27 16:12:47,276 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:47,312 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-27 16:12:47,324 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data
2025-07-27 16:12:47,364 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-27 16:12:47,365 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:47,401 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-27 16:12:47,413 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data
2025-07-27 16:12:47,453 - [BITVAVO] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-27 16:12:47,454 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:47,493 - [BITVAVO] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-27 16:12:47,507 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data
2025-07-27 16:12:47,555 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:47,556 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:47,608 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:47,628 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data
2025-07-27 16:12:47,696 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:47,696 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:47,759 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:47,780 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-07-27 16:12:47,849 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:47,930 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BTC/USDT using full OHLCV data
2025-07-27 16:12:48,001 - [BITVAVO] - root - WARNING - Found 6 extreme PGO values (abs > 10)
2025-07-27 16:12:48,001 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:48,065 - [BITVAVO] - root - WARNING - Found 6 extreme PGO values (abs > 10)
2025-07-27 16:12:48,085 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-07-27 16:12:48,153 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-27 16:12:48,153 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:48,216 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-27 16:12:48,236 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-07-27 16:12:48,308 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-27 16:12:48,308 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:48,356 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-27 16:12:48,373 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-07-27 16:12:48,441 - [BITVAVO] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-27 16:12:48,442 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:48,504 - [BITVAVO] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-27 16:12:48,524 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-07-27 16:12:48,592 - [BITVAVO] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-27 16:12:48,593 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:48,657 - [BITVAVO] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-27 16:12:48,678 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-07-27 16:12:48,746 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-27 16:12:48,746 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:48,809 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-27 16:12:48,828 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-07-27 16:12:48,896 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:48,896 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:48,959 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:48,979 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-07-27 16:12:49,047 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-27 16:12:49,047 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:49,112 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-27 16:12:49,132 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-07-27 16:12:49,200 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-27 16:12:49,200 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:49,258 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-27 16:12:49,270 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-07-27 16:12:49,312 - [BITVAVO] - root - WARNING - Found 6 extreme PGO values (abs > 10)
2025-07-27 16:12:49,312 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:49,350 - [BITVAVO] - root - WARNING - Found 6 extreme PGO values (abs > 10)
2025-07-27 16:12:49,363 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-07-27 16:12:49,402 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-27 16:12:49,403 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:49,438 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-27 16:12:49,450 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-07-27 16:12:49,489 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:49,489 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:49,526 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:49,539 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-07-27 16:12:49,580 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:49,632 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BTC/USDT using full OHLCV data
2025-07-27 16:12:49,677 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:49,729 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-07-27 16:12:49,770 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:49,834 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-07-27 16:12:49,890 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:49,891 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:49,949 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:49,970 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-07-27 16:12:50,031 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:50,105 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-07-27 16:12:50,161 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:50,225 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-07-27 16:12:50,266 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:50,317 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-07-27 16:12:50,355 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:50,408 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-07-27 16:12:50,450 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:50,499 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-07-27 16:12:50,537 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:50,537 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:50,572 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:50,584 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-07-27 16:12:50,622 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:50,669 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-07-27 16:12:50,708 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:50,759 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-07-27 16:12:50,804 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-27 16:12:50,804 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:50,848 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-27 16:12:50,868 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-07-27 16:12:50,911 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-27 16:12:50,911 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:50,950 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-27 16:12:50,961 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BTC/USDT using full OHLCV data
2025-07-27 16:12:51,001 - [BITVAVO] - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-07-27 16:12:51,001 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:51,040 - [BITVAVO] - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-07-27 16:12:51,055 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-07-27 16:12:51,094 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-27 16:12:51,094 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:51,130 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-27 16:12:51,142 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-07-27 16:12:51,180 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-27 16:12:51,180 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:51,215 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-27 16:12:51,227 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-07-27 16:12:51,267 - [BITVAVO] - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-07-27 16:12:51,267 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:51,305 - [BITVAVO] - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-07-27 16:12:51,318 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-07-27 16:12:51,359 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-27 16:12:51,359 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:51,396 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-27 16:12:51,408 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-07-27 16:12:51,449 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-27 16:12:51,449 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:51,489 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-27 16:12:51,500 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-07-27 16:12:51,541 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-27 16:12:51,541 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:51,583 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-27 16:12:51,596 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-07-27 16:12:51,653 - [BITVAVO] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-27 16:12:51,653 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:51,689 - [BITVAVO] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-27 16:12:51,701 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-07-27 16:12:51,740 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-27 16:12:51,740 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:51,777 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-27 16:12:51,789 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-07-27 16:12:51,827 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-27 16:12:51,827 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:51,862 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-27 16:12:51,874 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-07-27 16:12:51,915 - [BITVAVO] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-27 16:12:51,915 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:51,952 - [BITVAVO] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-27 16:12:51,966 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-07-27 16:12:52,013 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:52,014 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:52,059 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:52,071 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-07-27 16:12:52,114 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:52,166 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BTC/USDT using full OHLCV data
2025-07-27 16:12:52,207 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-27 16:12:52,207 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:52,243 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-27 16:12:52,256 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-07-27 16:12:52,296 - [BITVAVO] - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-07-27 16:12:52,296 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:52,336 - [BITVAVO] - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-07-27 16:12:52,351 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-07-27 16:12:52,398 - [BITVAVO] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-27 16:12:52,398 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:52,435 - [BITVAVO] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-27 16:12:52,448 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-07-27 16:12:52,488 - [BITVAVO] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-27 16:12:52,488 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:52,528 - [BITVAVO] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-27 16:12:52,542 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-07-27 16:12:52,582 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-27 16:12:52,582 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:52,626 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-27 16:12:52,639 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-07-27 16:12:52,681 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-27 16:12:52,681 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:52,717 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-27 16:12:52,729 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-07-27 16:12:52,772 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:52,820 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-07-27 16:12:52,864 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-27 16:12:52,864 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:52,912 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-27 16:12:52,930 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-07-27 16:12:52,986 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-27 16:12:52,986 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:53,038 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-27 16:12:53,056 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-07-27 16:12:53,115 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-27 16:12:53,116 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:53,168 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-27 16:12:53,186 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-07-27 16:12:53,243 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-27 16:12:53,243 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:53,296 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-27 16:12:53,314 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-07-27 16:12:53,371 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:53,439 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-07-27 16:12:53,496 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:53,551 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BTC/USDT using full OHLCV data
2025-07-27 16:12:53,590 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-27 16:12:53,590 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:53,642 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-27 16:12:53,654 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-07-27 16:12:53,695 - [BITVAVO] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-27 16:12:53,695 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:53,730 - [BITVAVO] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-27 16:12:53,744 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-07-27 16:12:53,783 - [BITVAVO] - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-07-27 16:12:53,783 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:53,820 - [BITVAVO] - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-07-27 16:12:53,833 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-07-27 16:12:53,877 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-27 16:12:53,877 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:53,914 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-27 16:12:53,926 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-07-27 16:12:53,969 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-27 16:12:53,970 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:54,007 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-27 16:12:54,021 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-07-27 16:12:54,080 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:54,140 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-07-27 16:12:54,188 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:54,238 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-07-27 16:12:54,287 - [BITVAVO] - root - WARNING - Found 6 extreme PGO values (abs > 10)
2025-07-27 16:12:54,287 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:54,327 - [BITVAVO] - root - WARNING - Found 6 extreme PGO values (abs > 10)
2025-07-27 16:12:54,347 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-07-27 16:12:54,396 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-27 16:12:54,397 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:54,435 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-27 16:12:54,449 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-07-27 16:12:54,497 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:54,498 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:54,534 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:54,546 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-07-27 16:12:54,601 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:54,655 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-07-27 16:12:54,695 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-27 16:12:54,696 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:54,732 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-27 16:12:54,745 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-07-27 16:12:54,788 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:54,789 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:54,828 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:54,840 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BTC/USDT using full OHLCV data
2025-07-27 16:12:54,882 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-27 16:12:54,882 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:54,920 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-27 16:12:54,932 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-07-27 16:12:54,972 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-27 16:12:54,972 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:55,010 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-27 16:12:55,022 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-07-27 16:12:55,063 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:55,112 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-07-27 16:12:55,163 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-27 16:12:55,163 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:55,202 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-27 16:12:55,215 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-07-27 16:12:55,258 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-27 16:12:55,259 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:55,301 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-27 16:12:55,316 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-07-27 16:12:55,362 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-27 16:12:55,363 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:55,405 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-27 16:12:55,419 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-07-27 16:12:55,461 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-27 16:12:55,461 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:55,498 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-27 16:12:55,512 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-07-27 16:12:55,557 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-27 16:12:55,558 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:55,593 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-27 16:12:55,605 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-07-27 16:12:55,645 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:55,646 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:55,682 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:55,695 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-07-27 16:12:55,736 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-27 16:12:55,736 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:55,771 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-27 16:12:55,783 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-07-27 16:12:55,823 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-27 16:12:55,823 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:55,862 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-27 16:12:55,875 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-07-27 16:12:55,916 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:55,917 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:55,954 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:55,968 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-07-27 16:12:56,021 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:56,083 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BTC/USDT using full OHLCV data
2025-07-27 16:12:56,131 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-27 16:12:56,131 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:56,172 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-27 16:12:56,185 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-07-27 16:12:56,228 - [BITVAVO] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-27 16:12:56,228 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:56,268 - [BITVAVO] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-27 16:12:56,282 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-07-27 16:12:56,319 - [BITVAVO] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-27 16:12:56,320 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:56,354 - [BITVAVO] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-27 16:12:56,371 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-07-27 16:12:56,408 - [BITVAVO] - root - WARNING - Found 6 extreme PGO values (abs > 10)
2025-07-27 16:12:56,409 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:56,442 - [BITVAVO] - root - WARNING - Found 6 extreme PGO values (abs > 10)
2025-07-27 16:12:56,454 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-07-27 16:12:56,491 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:56,491 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:56,526 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:56,537 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-07-27 16:12:56,575 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-27 16:12:56,575 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:56,609 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-27 16:12:56,621 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-07-27 16:12:56,659 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:56,706 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-07-27 16:12:56,748 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-27 16:12:56,748 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:56,789 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-27 16:12:56,802 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-07-27 16:12:56,842 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:56,842 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:56,878 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:56,890 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-07-27 16:12:56,930 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:56,930 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:56,966 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:56,979 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-07-27 16:12:57,019 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:57,020 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:57,077 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:57,093 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-07-27 16:12:57,150 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:57,150 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:57,189 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:57,201 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-07-27 16:12:57,242 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:57,242 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:57,280 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:57,292 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BTC/USDT using full OHLCV data
2025-07-27 16:12:57,334 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:57,335 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:57,375 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:57,390 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-07-27 16:12:57,446 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-27 16:12:57,446 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:57,484 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-27 16:12:57,496 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-07-27 16:12:57,549 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-27 16:12:57,550 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:57,592 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-27 16:12:57,604 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-07-27 16:12:57,655 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:57,656 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:57,693 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:57,705 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-07-27 16:12:57,743 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:57,743 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:57,786 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:57,802 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-07-27 16:12:57,849 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-27 16:12:57,850 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:57,908 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-27 16:12:57,928 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-07-27 16:12:57,988 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:57,988 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:58,033 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:58,045 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-07-27 16:12:58,087 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-27 16:12:58,087 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:58,128 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-27 16:12:58,140 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-07-27 16:12:58,179 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-27 16:12:58,179 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:58,221 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-27 16:12:58,232 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-07-27 16:12:58,280 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-27 16:12:58,280 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:58,322 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-27 16:12:58,334 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-07-27 16:12:58,377 - [BITVAVO] - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-07-27 16:12:58,377 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:58,413 - [BITVAVO] - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-07-27 16:12:58,427 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-07-27 16:12:58,470 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:58,519 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-07-27 16:12:58,562 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:58,619 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BTC/USDT using full OHLCV data
2025-07-27 16:12:58,657 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:58,702 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-07-27 16:12:58,741 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:58,787 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-07-27 16:12:58,839 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:58,896 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-07-27 16:12:58,949 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:58,949 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:58,990 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:59,001 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-07-27 16:12:59,039 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:59,107 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-07-27 16:12:59,151 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:59,199 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-07-27 16:12:59,242 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:59,291 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-07-27 16:12:59,334 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:59,385 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-07-27 16:12:59,427 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:59,478 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-07-27 16:12:59,520 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:59,569 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-07-27 16:12:59,614 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:59,663 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-07-27 16:12:59,705 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-27 16:12:59,705 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:59,744 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-27 16:12:59,756 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-07-27 16:12:59,794 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:59,842 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BTC/USDT using full OHLCV data
2025-07-27 16:12:59,880 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:59,881 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:12:59,916 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:12:59,927 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-07-27 16:12:59,968 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:12:59,968 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:00,007 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:13:00,020 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-07-27 16:13:00,065 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:00,115 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-07-27 16:13:00,157 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-27 16:13:00,157 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:00,193 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-27 16:13:00,207 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-07-27 16:13:00,258 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-27 16:13:00,258 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:00,299 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-27 16:13:00,311 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-07-27 16:13:00,353 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:00,411 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-07-27 16:13:00,460 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-27 16:13:00,460 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:00,508 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-27 16:13:00,520 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-07-27 16:13:00,583 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:00,662 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-07-27 16:13:00,701 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:13:00,702 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:00,737 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:13:00,748 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-07-27 16:13:00,789 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-27 16:13:00,790 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:00,842 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-27 16:13:00,861 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-07-27 16:13:00,905 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:13:00,905 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:00,956 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:13:00,968 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-07-27 16:13:01,009 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:01,060 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-07-27 16:13:01,111 - [BITVAVO] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-27 16:13:01,111 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:01,152 - [BITVAVO] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-27 16:13:01,167 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/BTC/USDT using full OHLCV data
2025-07-27 16:13:01,206 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-27 16:13:01,207 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:01,244 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-27 16:13:01,256 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-07-27 16:13:01,319 - [BITVAVO] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-27 16:13:01,319 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:01,358 - [BITVAVO] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-27 16:13:01,374 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-07-27 16:13:01,419 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-27 16:13:01,419 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:01,463 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-27 16:13:01,475 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-07-27 16:13:01,524 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:01,576 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-07-27 16:13:01,616 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-27 16:13:01,617 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:01,658 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-27 16:13:01,671 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-07-27 16:13:01,712 - [BITVAVO] - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-07-27 16:13:01,713 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:01,760 - [BITVAVO] - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-07-27 16:13:01,772 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-07-27 16:13:01,812 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-27 16:13:01,812 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:01,850 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-27 16:13:01,862 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-07-27 16:13:01,903 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:13:01,903 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:01,944 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:13:01,956 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-07-27 16:13:01,996 - [BITVAVO] - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-07-27 16:13:01,996 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:02,032 - [BITVAVO] - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-07-27 16:13:02,045 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-07-27 16:13:02,085 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-27 16:13:02,085 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:02,124 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-27 16:13:02,137 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-07-27 16:13:02,179 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-27 16:13:02,179 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:02,217 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-27 16:13:02,229 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-07-27 16:13:02,273 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:13:02,273 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:02,326 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:13:02,340 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-07-27 16:13:02,397 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:02,453 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BTC/USDT using full OHLCV data
2025-07-27 16:13:02,493 - [BITVAVO] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-27 16:13:02,494 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:02,529 - [BITVAVO] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-27 16:13:02,541 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-07-27 16:13:02,588 - [BITVAVO] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-27 16:13:02,589 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:02,628 - [BITVAVO] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-27 16:13:02,643 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-07-27 16:13:02,687 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-27 16:13:02,687 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:02,723 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-27 16:13:02,735 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-07-27 16:13:02,782 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-27 16:13:02,782 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:02,820 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-27 16:13:02,833 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-07-27 16:13:02,875 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:13:02,876 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:02,922 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-27 16:13:02,940 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-07-27 16:13:02,996 - [BITVAVO] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-27 16:13:02,996 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:03,047 - [BITVAVO] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-27 16:13:03,065 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-07-27 16:13:03,120 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:13:03,120 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:03,172 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-27 16:13:03,189 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-07-27 16:13:03,245 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:03,314 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-07-27 16:13:03,377 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-27 16:13:03,378 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:03,432 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-27 16:13:03,450 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-07-27 16:13:03,505 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-27 16:13:03,506 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:03,559 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-27 16:13:03,576 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-07-27 16:13:03,615 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-27 16:13:03,615 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-27 16:13:03,650 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-27 16:13:03,662 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-07-27 16:13:09,992 - [BITVAVO] - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-07-27 16:13:09,993 - [BITVAVO] - root - INFO - Latest MTPI signal is 1
2025-07-27 16:13:09,993 - [BITVAVO] - root - INFO - Latest MTPI signal is bullish (1), will proceed with normal asset selection
2025-07-27 16:13:09,994 - [BITVAVO] - root - INFO - Finished calculating daily scores. DataFrame shape: (287, 14)
2025-07-27 16:13:09,994 - [BITVAVO] - root - WARNING - Running strategy with n_assets=2, weighted allocation (50.0%, 50.0%), MTPI filtering ENABLED
2025-07-27 16:13:10,005 - [BITVAVO] - root - INFO - Date ranges for each asset:
2025-07-27 16:13:10,005 - [BITVAVO] - root - INFO -   ETH/USDT: 2024-10-13 to 2025-07-26 (287 candles)
2025-07-27 16:13:10,006 - [BITVAVO] - root - INFO -   BTC/USDT: 2024-10-13 to 2025-07-26 (287 candles)
2025-07-27 16:13:10,006 - [BITVAVO] - root - INFO -   SOL/USDT: 2024-10-13 to 2025-07-26 (287 candles)
2025-07-27 16:13:10,007 - [BITVAVO] - root - INFO -   SUI/USDT: 2024-10-13 to 2025-07-26 (287 candles)
2025-07-27 16:13:10,007 - [BITVAVO] - root - INFO -   XRP/USDT: 2024-10-13 to 2025-07-26 (287 candles)
2025-07-27 16:13:10,008 - [BITVAVO] - root - INFO -   AAVE/USDT: 2024-10-13 to 2025-07-26 (287 candles)
2025-07-27 16:13:10,008 - [BITVAVO] - root - INFO -   AVAX/USDT: 2024-10-13 to 2025-07-26 (287 candles)
2025-07-27 16:13:10,008 - [BITVAVO] - root - INFO -   ADA/USDT: 2024-10-13 to 2025-07-26 (287 candles)
2025-07-27 16:13:10,009 - [BITVAVO] - root - INFO -   LINK/USDT: 2024-10-13 to 2025-07-26 (287 candles)
2025-07-27 16:13:10,009 - [BITVAVO] - root - INFO -   TRX/USDT: 2024-10-13 to 2025-07-26 (287 candles)
2025-07-27 16:13:10,010 - [BITVAVO] - root - INFO -   PEPE/USDT: 2024-10-13 to 2025-07-26 (287 candles)
2025-07-27 16:13:10,010 - [BITVAVO] - root - INFO -   DOGE/USDT: 2024-10-13 to 2025-07-26 (287 candles)
2025-07-27 16:13:10,010 - [BITVAVO] - root - INFO -   BNB/USDT: 2024-10-13 to 2025-07-26 (287 candles)
2025-07-27 16:13:10,011 - [BITVAVO] - root - INFO -   DOT/USDT: 2024-10-13 to 2025-07-26 (287 candles)
2025-07-27 16:13:10,012 - [BITVAVO] - root - INFO - Common dates range: 2024-10-13 to 2025-07-26 (287 candles)
2025-07-27 16:13:10,013 - [BITVAVO] - root - INFO - Analysis will run from: 2025-02-10 to 2025-07-26 (167 candles)
2025-07-27 16:13:10,022 - [BITVAVO] - root - INFO - EXECUTION TIMING VERIFICATION:
2025-07-27 16:13:10,023 - [BITVAVO] - root - INFO -    Execution Method: candle_close
2025-07-27 16:13:10,024 - [BITVAVO] - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-07-27 16:13:10,024 - [BITVAVO] - root - INFO -    Signal generated and executed immediately
2025-07-27 16:13:10,038 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-10 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 12.0, 'SOL/USDT': 11.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 3.0, 'LINK/USDT': 8.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 9.0, 'DOT/USDT': 4.0}
2025-07-27 16:13:10,039 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 7.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,040 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,040 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 11.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,041 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,041 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,041 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,042 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,042 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 3.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,042 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,043 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,043 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 0.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,043 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 3.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,044 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,044 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,047 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-11 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 10.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 11.0, 'DOT/USDT': 3.0}
2025-07-27 16:13:10,047 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,048 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,048 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 10.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,048 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,049 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,049 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 5.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,050 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,050 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 5.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,051 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,051 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,051 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 0.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,052 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,052 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,052 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 3.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,054 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-12 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 10.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 4.0}
2025-07-27 16:13:10,055 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,055 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,056 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 10.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,056 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 3.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,057 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,057 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 3.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,058 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,058 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,058 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,059 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,059 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 0.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,059 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,060 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,060 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,063 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-13 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 10.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 6.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 4.0}
2025-07-27 16:13:10,063 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 6.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,063 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,064 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 10.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,064 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,064 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,065 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,065 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,065 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 6.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,066 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,066 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,066 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 0.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,067 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,067 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,068 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,070 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-14 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 9.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 6.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 4.0}
2025-07-27 16:13:10,070 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,071 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,071 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 9.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,071 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,072 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,072 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,073 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,073 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,074 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,074 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,075 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 0.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,075 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,075 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,076 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-10-13 00:00:00+00:00)
2025-07-27 16:13:10,078 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-15 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 9.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 6.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 4.0}
2025-07-27 16:13:10,080 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-16 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 6.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 4.0}
2025-07-27 16:13:10,082 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-17 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 11.0, 'SOL/USDT': 2.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 7.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 7.0, 'LINK/USDT': 5.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 5.0}
2025-07-27 16:13:10,084 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-18 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 11.0, 'SOL/USDT': 2.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 7.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 7.0, 'LINK/USDT': 5.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 12.0, 'DOT/USDT': 5.0}
2025-07-27 16:13:10,086 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-19 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 11.0, 'SOL/USDT': 2.0, 'SUI/USDT': 4.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 7.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 7.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-27 16:13:10,088 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-20 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 11.0, 'SOL/USDT': 1.0, 'SUI/USDT': 5.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 7.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 7.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-27 16:13:10,089 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-21 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 1.0, 'SUI/USDT': 5.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 2.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 7.0}
2025-07-27 16:13:10,091 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-22 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 6.0, 'LINK/USDT': 2.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 7.0}
2025-07-27 16:13:10,094 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-23 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 6.0, 'LINK/USDT': 2.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 7.0}
2025-07-27 16:13:10,095 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-24 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 6.0, 'LINK/USDT': 2.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 7.0}
2025-07-27 16:13:10,097 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-25 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 6.0, 'LINK/USDT': 2.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,100 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-26 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 10.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 1.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 2.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 12.0, 'DOT/USDT': 10.0}
2025-07-27 16:13:10,101 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-27 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 10.0, 'SOL/USDT': 0.0, 'SUI/USDT': 5.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 5.0, 'LINK/USDT': 1.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 2.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 12.0, 'DOT/USDT': 11.0}
2025-07-27 16:13:10,103 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-28 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 10.0, 'SOL/USDT': 0.0, 'SUI/USDT': 5.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 5.0, 'LINK/USDT': 1.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 2.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 12.0, 'DOT/USDT': 11.0}
2025-07-27 16:13:10,105 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-01 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 10.0, 'SOL/USDT': 0.0, 'SUI/USDT': 5.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 6.0, 'LINK/USDT': 1.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 11.0}
2025-07-27 16:13:10,106 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-02 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 8.0, 'SOL/USDT': 3.0, 'SUI/USDT': 5.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 13.0, 'LINK/USDT': 2.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 9.0, 'DOT/USDT': 9.0}
2025-07-27 16:13:10,108 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-03 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 8.0, 'SOL/USDT': 3.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 13.0, 'LINK/USDT': 2.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 9.0, 'DOT/USDT': 9.0}
2025-07-27 16:13:10,110 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-04 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 1.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 13.0, 'LINK/USDT': 3.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,111 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-05 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 1.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 7.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 13.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,113 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-06 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 3.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 13.0, 'LINK/USDT': 8.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 7.0}
2025-07-27 16:13:10,115 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-07 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 3.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 13.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 7.0}
2025-07-27 16:13:10,117 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-08 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 3.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 13.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 7.0}
2025-07-27 16:13:10,119 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-09 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 12.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,120 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-10 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 7.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 12.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,122 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-11 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 7.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 12.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,123 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-12 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 12.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 4.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,125 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-13 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 4.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,128 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-14 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,130 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-15 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 11.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,132 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-16 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 10.0, 'LINK/USDT': 5.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 11.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,136 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-17 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 10.0, 'LINK/USDT': 5.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 6.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,139 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-18 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 9.0, 'LINK/USDT': 5.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 6.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-27 16:13:10,142 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-19 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 9.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 6.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 12.0, 'DOT/USDT': 9.0}
2025-07-27 16:13:10,145 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-20 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 8.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 12.0, 'DOT/USDT': 9.0}
2025-07-27 16:13:10,146 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-21 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 8.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 12.0, 'DOT/USDT': 9.0}
2025-07-27 16:13:10,148 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-22 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 8.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 12.0, 'DOT/USDT': 9.0}
2025-07-27 16:13:10,150 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-23 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 7.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 12.0, 'DOT/USDT': 9.0}
2025-07-27 16:13:10,152 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-24 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 6.0, 'SOL/USDT': 4.0, 'SUI/USDT': 0.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 12.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,156 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-25 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 5.0, 'SOL/USDT': 4.0, 'SUI/USDT': 0.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 12.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 10.0, 'DOT/USDT': 7.0}
2025-07-27 16:13:10,158 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-26 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 5.0, 'SOL/USDT': 2.0, 'SUI/USDT': 2.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 12.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 4.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 9.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,159 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-27 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 12.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 3.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 9.0, 'DOT/USDT': 7.0}
2025-07-27 16:13:10,160 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-28 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 5.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 12.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 6.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 10.0, 'DOT/USDT': 7.0}
2025-07-27 16:13:10,162 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-29 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 7.0}
2025-07-27 16:13:10,164 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-30 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 7.0}
2025-07-27 16:13:10,165 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-31 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-27 16:13:10,166 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-01 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-27 16:13:10,168 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-02 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 2.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-27 16:13:10,169 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-03 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 10.0, 'SOL/USDT': 1.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 7.0}
2025-07-27 16:13:10,170 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-04 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 1.0, 'SUI/USDT': 8.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-27 16:13:10,172 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-05 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 1.0, 'SUI/USDT': 8.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-27 16:13:10,173 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-06 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 1.0, 'SUI/USDT': 5.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 11.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,175 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-07 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 2.0, 'SUI/USDT': 5.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 10.0, 'DOT/USDT': 7.0}
2025-07-27 16:13:10,176 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-08 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 4.0, 'SUI/USDT': 5.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 11.0, 'DOT/USDT': 5.0}
2025-07-27 16:13:10,178 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-09 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 5.0, 'SUI/USDT': 6.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 1.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 11.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,179 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-10 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 5.0, 'SUI/USDT': 6.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 11.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,181 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-11 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 6.0, 'SUI/USDT': 6.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 10.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,183 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-12 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 7.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,184 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-13 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 8.0, 'SUI/USDT': 5.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 7.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,186 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-14 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 8.0, 'SUI/USDT': 5.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 7.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,187 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-15 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 8.0, 'SUI/USDT': 5.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,189 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-16 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 11.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,190 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-17 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 12.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,191 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-18 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 12.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,193 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-19 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 13.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 2.0, 'LINK/USDT': 5.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,195 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-20 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 13.0, 'SUI/USDT': 3.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 3.0}
2025-07-27 16:13:10,196 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-21 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 13.0, 'SUI/USDT': 3.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,197 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-22 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 9.0, 'SOL/USDT': 12.0, 'SUI/USDT': 9.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 2.0, 'LINK/USDT': 5.0, 'TRX/USDT': 6.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 5.0, 'DOT/USDT': 1.0}
2025-07-27 16:13:10,198 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-04-23:
2025-07-27 16:13:10,198 - [BITVAVO] - root - INFO -    Signal Date: 2025-04-22 (generated at 00:00 UTC)
2025-07-27 16:13:10,198 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-04-23 00:00 UTC (immediate)
2025-07-27 16:13:10,198 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-27 16:13:10,198 - [BITVAVO] - root - INFO -    Buying: ['SOL/USDT', 'PEPE/USDT']
2025-07-27 16:13:10,199 - [BITVAVO] - root - INFO -    SOL/USDT buy price: $151.1000 (close price)
2025-07-27 16:13:10,199 - [BITVAVO] - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-07-27 16:13:10,200 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-23 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 2.0, 'LINK/USDT': 8.0, 'TRX/USDT': 4.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 1.0}
2025-07-27 16:13:10,201 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-07-27 16:13:10,201 - [BITVAVO] - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-07-27 16:13:10,202 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-07-27 16:13:10,202 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-27 16:13:10,202 - [BITVAVO] - root - INFO -    Selling: ['PEPE/USDT']
2025-07-27 16:13:10,202 - [BITVAVO] - root - INFO -    Buying: ['SUI/USDT']
2025-07-27 16:13:10,203 - [BITVAVO] - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-07-27 16:13:10,205 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-24 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 4.0, 'LINK/USDT': 8.0, 'TRX/USDT': 3.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-27 16:13:10,207 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-25 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 4.0, 'LINK/USDT': 8.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-27 16:13:10,207 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-04-26:
2025-07-27 16:13:10,208 - [BITVAVO] - root - INFO -    Signal Date: 2025-04-25 (generated at 00:00 UTC)
2025-07-27 16:13:10,208 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-04-26 00:00 UTC (immediate)
2025-07-27 16:13:10,209 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-27 16:13:10,209 - [BITVAVO] - root - INFO -    Selling: ['SOL/USDT']
2025-07-27 16:13:10,210 - [BITVAVO] - root - INFO -    Buying: ['PEPE/USDT']
2025-07-27 16:13:10,210 - [BITVAVO] - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-07-27 16:13:10,213 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-26 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 4.0, 'LINK/USDT': 7.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-27 16:13:10,215 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-27 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-27 16:13:10,217 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-28 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-27 16:13:10,219 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-29 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 7.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-27 16:13:10,221 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-30 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 7.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-27 16:13:10,223 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-01 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-27 16:13:10,225 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-02 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 7.0, 'SOL/USDT': 10.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-27 16:13:10,229 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-03 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,229 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-05-04:
2025-07-27 16:13:10,230 - [BITVAVO] - root - INFO -    Signal Date: 2025-05-03 (generated at 00:00 UTC)
2025-07-27 16:13:10,230 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-04 00:00 UTC (immediate)
2025-07-27 16:13:10,230 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-27 16:13:10,230 - [BITVAVO] - root - INFO -    Selling: ['PEPE/USDT']
2025-07-27 16:13:10,230 - [BITVAVO] - root - INFO -    Buying: ['AAVE/USDT']
2025-07-27 16:13:10,230 - [BITVAVO] - root - INFO -    AAVE/USDT buy price: $171.1100 (close price)
2025-07-27 16:13:10,232 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-04 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 9.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,234 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-05 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 9.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,237 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-06 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 10.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,238 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-07 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 10.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 0.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-27 16:13:10,240 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-08 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 6.0, 'SOL/USDT': 9.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-27 16:13:10,241 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-05-09:
2025-07-27 16:13:10,241 - [BITVAVO] - root - INFO -    Signal Date: 2025-05-08 (generated at 00:00 UTC)
2025-07-27 16:13:10,242 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-09 00:00 UTC (immediate)
2025-07-27 16:13:10,242 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-27 16:13:10,242 - [BITVAVO] - root - INFO -    Selling: ['AAVE/USDT']
2025-07-27 16:13:10,243 - [BITVAVO] - root - INFO -    Buying: ['PEPE/USDT']
2025-07-27 16:13:10,243 - [BITVAVO] - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-07-27 16:13:10,244 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-09 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 4.0, 'SOL/USDT': 9.0, 'SUI/USDT': 12.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 1.0, 'DOT/USDT': 7.0}
2025-07-27 16:13:10,247 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-10 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 12.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,249 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-11 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 12.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,251 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-12 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 11.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,252 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-05-13:
2025-07-27 16:13:10,252 - [BITVAVO] - root - INFO -    Signal Date: 2025-05-12 (generated at 00:00 UTC)
2025-07-27 16:13:10,252 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-13 00:00 UTC (immediate)
2025-07-27 16:13:10,253 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-27 16:13:10,253 - [BITVAVO] - root - INFO -    Selling: ['SUI/USDT']
2025-07-27 16:13:10,253 - [BITVAVO] - root - INFO -    Buying: ['ETH/USDT']
2025-07-27 16:13:10,254 - [BITVAVO] - root - INFO -    ETH/USDT buy price: $2679.7100 (close price)
2025-07-27 16:13:10,255 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-13 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,257 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-14 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,259 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-15 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,260 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-16 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,261 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-17 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 4.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,263 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-18 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 4.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,266 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-19 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 5.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,266 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-05-20:
2025-07-27 16:13:10,266 - [BITVAVO] - root - INFO -    Signal Date: 2025-05-19 (generated at 00:00 UTC)
2025-07-27 16:13:10,267 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-20 00:00 UTC (immediate)
2025-07-27 16:13:10,267 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-27 16:13:10,267 - [BITVAVO] - root - INFO -    Selling: ['ETH/USDT']
2025-07-27 16:13:10,267 - [BITVAVO] - root - INFO -    Buying: ['AAVE/USDT']
2025-07-27 16:13:10,268 - [BITVAVO] - root - INFO -    AAVE/USDT buy price: $259.4600 (close price)
2025-07-27 16:13:10,269 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-20 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-27 16:13:10,271 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-21 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 6.0}
2025-07-27 16:13:10,273 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-22 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 1.0, 'DOT/USDT': 6.0}
2025-07-27 16:13:10,275 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-23 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 7.0, 'SOL/USDT': 9.0, 'SUI/USDT': 7.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 2.0, 'DOT/USDT': 6.0}
2025-07-27 16:13:10,277 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-24 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 7.0, 'SOL/USDT': 9.0, 'SUI/USDT': 6.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 2.0, 'LINK/USDT': 1.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 4.0, 'DOT/USDT': 5.0}
2025-07-27 16:13:10,279 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-25 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 9.0, 'SUI/USDT': 3.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 2.0, 'LINK/USDT': 1.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 5.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,280 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-26 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 9.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 2.0, 'TRX/USDT': 4.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 6.0, 'DOT/USDT': 1.0}
2025-07-27 16:13:10,282 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-27 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 9.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 2.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 7.0, 'DOT/USDT': 1.0}
2025-07-27 16:13:10,284 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-28 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 7.0, 'SOL/USDT': 8.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 2.0, 'LINK/USDT': 2.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-27 16:13:10,286 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-29 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 2.0, 'LINK/USDT': 2.0, 'TRX/USDT': 6.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-27 16:13:10,288 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-30 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 0.0}
2025-07-27 16:13:10,289 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-05-31:
2025-07-27 16:13:10,290 - [BITVAVO] - root - INFO -    Signal Date: 2025-05-30 (generated at 00:00 UTC)
2025-07-27 16:13:10,290 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-31 00:00 UTC (immediate)
2025-07-27 16:13:10,291 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-27 16:13:10,291 - [BITVAVO] - root - INFO -    Selling: ['PEPE/USDT']
2025-07-27 16:13:10,291 - [BITVAVO] - root - INFO -    Buying: ['ETH/USDT']
2025-07-27 16:13:10,291 - [BITVAVO] - root - INFO -    ETH/USDT buy price: $2528.0600 (close price)
2025-07-27 16:13:10,293 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-31 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 0.0}
2025-07-27 16:13:10,295 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-01 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 0.0}
2025-07-27 16:13:10,296 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-02 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 10.0, 'DOT/USDT': 0.0}
2025-07-27 16:13:10,298 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-03 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 10.0, 'DOT/USDT': 0.0}
2025-07-27 16:13:10,300 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-04 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 9.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 9.0, 'DOT/USDT': 0.0}
2025-07-27 16:13:10,301 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-05 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 2.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,302 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-06-06:
2025-07-27 16:13:10,302 - [BITVAVO] - root - INFO -    Signal Date: 2025-06-05 (generated at 00:00 UTC)
2025-07-27 16:13:10,302 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-06-06 00:00 UTC (immediate)
2025-07-27 16:13:10,303 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-27 16:13:10,303 - [BITVAVO] - root - INFO -    Selling: ['ETH/USDT']
2025-07-27 16:13:10,303 - [BITVAVO] - root - INFO -    Buying: ['TRX/USDT']
2025-07-27 16:13:10,303 - [BITVAVO] - root - INFO -    TRX/USDT buy price: $0.2779 (close price)
2025-07-27 16:13:10,305 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-06 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,307 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-07 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 5.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,309 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-08 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 5.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,310 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-09 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 5.0, 'SUI/USDT': 1.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-27 16:13:10,313 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-10 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-27 16:13:10,313 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-06-11:
2025-07-27 16:13:10,313 - [BITVAVO] - root - INFO -    Signal Date: 2025-06-10 (generated at 00:00 UTC)
2025-07-27 16:13:10,314 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-06-11 00:00 UTC (immediate)
2025-07-27 16:13:10,314 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-27 16:13:10,314 - [BITVAVO] - root - INFO -    Selling: ['TRX/USDT']
2025-07-27 16:13:10,315 - [BITVAVO] - root - INFO -    Buying: ['ETH/USDT']
2025-07-27 16:13:10,315 - [BITVAVO] - root - INFO -    ETH/USDT buy price: $2771.6100 (close price)
2025-07-27 16:13:10,318 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-11 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-27 16:13:10,319 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-12 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-27 16:13:10,321 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-13 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 5.0, 'SUI/USDT': 1.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-27 16:13:10,323 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-14 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 5.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-27 16:13:10,325 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-15 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-27 16:13:10,327 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-16 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 3.0}
2025-07-27 16:13:10,328 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-17 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 4.0}
2025-07-27 16:13:10,330 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-18 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 4.0}
2025-07-27 16:13:10,334 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-19 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 3.0}
2025-07-27 16:13:10,336 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-06-20:
2025-07-27 16:13:10,337 - [BITVAVO] - root - INFO -    Signal Date: 2025-06-19 (generated at 00:00 UTC)
2025-07-27 16:13:10,337 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-06-20 00:00 UTC (immediate)
2025-07-27 16:13:10,337 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-27 16:13:10,337 - [BITVAVO] - root - INFO -    Selling: ['ETH/USDT']
2025-07-27 16:13:10,337 - [BITVAVO] - root - INFO -    Buying: ['TRX/USDT']
2025-07-27 16:13:10,338 - [BITVAVO] - root - INFO -    TRX/USDT buy price: $0.2722 (close price)
2025-07-27 16:13:10,339 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-20 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 3.0}
2025-07-27 16:13:10,341 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-07-27 16:13:10,342 - [BITVAVO] - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-07-27 16:13:10,342 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-07-27 16:13:10,342 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-27 16:13:10,343 - [BITVAVO] - root - INFO -    Selling: ['AAVE/USDT', 'TRX/USDT']
2025-07-27 16:13:10,345 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-21 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 11.0, 'DOT/USDT': 4.0}
2025-07-27 16:13:10,347 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-22 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 11.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,349 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-23 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-27 16:13:10,350 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-24 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-27 16:13:10,352 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-25 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-27 16:13:10,354 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-26 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-27 16:13:10,356 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-27 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-27 16:13:10,358 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-28 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 13.0, 'SOL/USDT': 7.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-27 16:13:10,359 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-29 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 10.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,361 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-30 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,362 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-01 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,364 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-02 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,366 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-03 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-27 16:13:10,368 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-04 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-27 16:13:10,370 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-05 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-27 16:13:10,371 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-06 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-27 16:13:10,373 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-07 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-27 16:13:10,375 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-08 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 6.0, 'DOT/USDT': 0.0}
2025-07-27 16:13:10,377 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-09 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 9.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 4.0, 'DOT/USDT': 0.0}
2025-07-27 16:13:10,378 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-10 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 4.0, 'SUI/USDT': 10.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 3.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-27 16:13:10,379 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-07-11:
2025-07-27 16:13:10,379 - [BITVAVO] - root - INFO -    Signal Date: 2025-07-10 (generated at 00:00 UTC)
2025-07-27 16:13:10,380 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-07-11 00:00 UTC (immediate)
2025-07-27 16:13:10,381 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-27 16:13:10,381 - [BITVAVO] - root - INFO -    Buying: ['XRP/USDT', 'SUI/USDT']
2025-07-27 16:13:10,381 - [BITVAVO] - root - INFO -    XRP/USDT buy price: $2.7322 (close price)
2025-07-27 16:13:10,382 - [BITVAVO] - root - INFO -    SUI/USDT buy price: $3.3868 (close price)
2025-07-27 16:13:10,386 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-11 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 3.0, 'SOL/USDT': 3.0, 'SUI/USDT': 10.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 8.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-27 16:13:10,387 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-07-12:
2025-07-27 16:13:10,387 - [BITVAVO] - root - INFO -    Signal Date: 2025-07-11 (generated at 00:00 UTC)
2025-07-27 16:13:10,387 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-07-12 00:00 UTC (immediate)
2025-07-27 16:13:10,388 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-27 16:13:10,388 - [BITVAVO] - root - INFO -    Selling: ['SUI/USDT']
2025-07-27 16:13:10,389 - [BITVAVO] - root - INFO -    Buying: ['ETH/USDT']
2025-07-27 16:13:10,389 - [BITVAVO] - root - INFO -    ETH/USDT buy price: $2943.2800 (close price)
2025-07-27 16:13:10,391 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-12 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 3.0, 'SUI/USDT': 10.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 9.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-27 16:13:10,392 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-07-13:
2025-07-27 16:13:10,392 - [BITVAVO] - root - INFO -    Signal Date: 2025-07-12 (generated at 00:00 UTC)
2025-07-27 16:13:10,392 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-07-13 00:00 UTC (immediate)
2025-07-27 16:13:10,393 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-27 16:13:10,393 - [BITVAVO] - root - INFO -    Selling: ['ETH/USDT']
2025-07-27 16:13:10,393 - [BITVAVO] - root - INFO -    Buying: ['SUI/USDT']
2025-07-27 16:13:10,394 - [BITVAVO] - root - INFO -    SUI/USDT buy price: $3.4899 (close price)
2025-07-27 16:13:10,395 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-13 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 9.0, 'XRP/USDT': 13.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,396 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-07-14:
2025-07-27 16:13:10,396 - [BITVAVO] - root - INFO -    Signal Date: 2025-07-13 (generated at 00:00 UTC)
2025-07-27 16:13:10,396 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-07-14 00:00 UTC (immediate)
2025-07-27 16:13:10,397 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-27 16:13:10,397 - [BITVAVO] - root - INFO -    Selling: ['SUI/USDT']
2025-07-27 16:13:10,397 - [BITVAVO] - root - INFO -    Buying: ['ADA/USDT']
2025-07-27 16:13:10,398 - [BITVAVO] - root - INFO -    ADA/USDT buy price: $0.7352 (close price)
2025-07-27 16:13:10,399 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-14 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 12.0, 'XRP/USDT': 13.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-27 16:13:10,399 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-07-15:
2025-07-27 16:13:10,400 - [BITVAVO] - root - INFO -    Signal Date: 2025-07-14 (generated at 00:00 UTC)
2025-07-27 16:13:10,400 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-07-15 00:00 UTC (immediate)
2025-07-27 16:13:10,400 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-27 16:13:10,401 - [BITVAVO] - root - INFO -    Selling: ['ADA/USDT']
2025-07-27 16:13:10,401 - [BITVAVO] - root - INFO -    Buying: ['SUI/USDT']
2025-07-27 16:13:10,402 - [BITVAVO] - root - INFO -    SUI/USDT buy price: $4.1014 (close price)
2025-07-27 16:13:10,404 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-15 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 2.0, 'SUI/USDT': 13.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-27 16:13:10,406 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-16 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 2.0, 'SOL/USDT': 3.0, 'SUI/USDT': 13.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 9.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-27 16:13:10,408 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-17 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 1.0, 'SOL/USDT': 3.0, 'SUI/USDT': 12.0, 'XRP/USDT': 13.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 10.0, 'LINK/USDT': 8.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-27 16:13:10,409 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-18 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 0.0, 'SOL/USDT': 3.0, 'SUI/USDT': 8.0, 'XRP/USDT': 13.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 10.0, 'LINK/USDT': 8.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 1.0, 'DOT/USDT': 4.0}
2025-07-27 16:13:10,410 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-07-19:
2025-07-27 16:13:10,410 - [BITVAVO] - root - INFO -    Signal Date: 2025-07-18 (generated at 00:00 UTC)
2025-07-27 16:13:10,411 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-07-19 00:00 UTC (immediate)
2025-07-27 16:13:10,411 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-27 16:13:10,411 - [BITVAVO] - root - INFO -    Selling: ['SUI/USDT']
2025-07-27 16:13:10,412 - [BITVAVO] - root - INFO -    Buying: ['ETH/USDT']
2025-07-27 16:13:10,412 - [BITVAVO] - root - INFO -    ETH/USDT buy price: $3592.0100 (close price)
2025-07-27 16:13:10,414 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-19 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 0.0, 'SOL/USDT': 3.0, 'SUI/USDT': 7.0, 'XRP/USDT': 13.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 10.0, 'LINK/USDT': 9.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 11.0, 'BNB/USDT': 1.0, 'DOT/USDT': 5.0}
2025-07-27 16:13:10,416 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-20 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 0.0, 'SOL/USDT': 3.0, 'SUI/USDT': 6.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 10.0, 'LINK/USDT': 9.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 13.0, 'BNB/USDT': 2.0, 'DOT/USDT': 5.0}
2025-07-27 16:13:10,417 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-07-21:
2025-07-27 16:13:10,417 - [BITVAVO] - root - INFO -    Signal Date: 2025-07-20 (generated at 00:00 UTC)
2025-07-27 16:13:10,418 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-07-21 00:00 UTC (immediate)
2025-07-27 16:13:10,418 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-27 16:13:10,418 - [BITVAVO] - root - INFO -    Selling: ['ETH/USDT']
2025-07-27 16:13:10,418 - [BITVAVO] - root - INFO -    Buying: ['DOGE/USDT']
2025-07-27 16:13:10,419 - [BITVAVO] - root - INFO -    DOGE/USDT buy price: $0.2714 (close price)
2025-07-27 16:13:10,421 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-21 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 0.0, 'SOL/USDT': 5.0, 'SUI/USDT': 6.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 10.0, 'LINK/USDT': 9.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 13.0, 'BNB/USDT': 2.0, 'DOT/USDT': 4.0}
2025-07-27 16:13:10,423 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-22 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 0.0, 'SOL/USDT': 5.0, 'SUI/USDT': 5.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 11.0, 'LINK/USDT': 9.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 13.0, 'BNB/USDT': 3.0, 'DOT/USDT': 4.0}
2025-07-27 16:13:10,424 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-23 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 0.0, 'SOL/USDT': 5.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 10.0, 'LINK/USDT': 9.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 13.0, 'BNB/USDT': 3.0, 'DOT/USDT': 3.0}
2025-07-27 16:13:10,425 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-07-24:
2025-07-27 16:13:10,426 - [BITVAVO] - root - INFO -    Signal Date: 2025-07-23 (generated at 00:00 UTC)
2025-07-27 16:13:10,426 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-07-24 00:00 UTC (immediate)
2025-07-27 16:13:10,426 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-27 16:13:10,427 - [BITVAVO] - root - INFO -    Selling: ['XRP/USDT']
2025-07-27 16:13:10,427 - [BITVAVO] - root - INFO -    Buying: ['ETH/USDT']
2025-07-27 16:13:10,428 - [BITVAVO] - root - INFO -    ETH/USDT buy price: $3706.9400 (close price)
2025-07-27 16:13:10,429 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-24 00:00:00+00:00: {'ETH/USDT': 13.0, 'BTC/USDT': 1.0, 'SOL/USDT': 5.0, 'SUI/USDT': 6.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 10.0, 'LINK/USDT': 9.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 12.0, 'BNB/USDT': 4.0, 'DOT/USDT': 3.0}
2025-07-27 16:13:10,431 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-25 00:00:00+00:00: {'ETH/USDT': 13.0, 'BTC/USDT': 1.0, 'SOL/USDT': 5.0, 'SUI/USDT': 7.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 10.0, 'LINK/USDT': 9.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 12.0, 'BNB/USDT': 4.0, 'DOT/USDT': 3.0}
2025-07-27 16:13:10,537 - [BITVAVO] - root - INFO - Entry trade at 2025-04-23 00:00:00+00:00: SOL/USDT,PEPE/USDT
2025-07-27 16:13:10,538 - [BITVAVO] - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT,PEPE/USDT -> SOL/USDT,SUI/USDT
2025-07-27 16:13:10,539 - [BITVAVO] - root - INFO - Swap trade at 2025-04-26 00:00:00+00:00: SOL/USDT,SUI/USDT -> SUI/USDT,PEPE/USDT
2025-07-27 16:13:10,539 - [BITVAVO] - root - INFO - Swap trade at 2025-05-04 00:00:00+00:00: SUI/USDT,PEPE/USDT -> SUI/USDT,AAVE/USDT
2025-07-27 16:13:10,540 - [BITVAVO] - root - INFO - Swap trade at 2025-05-09 00:00:00+00:00: SUI/USDT,AAVE/USDT -> SUI/USDT,PEPE/USDT
2025-07-27 16:13:10,540 - [BITVAVO] - root - INFO - Swap trade at 2025-05-13 00:00:00+00:00: SUI/USDT,PEPE/USDT -> ETH/USDT,PEPE/USDT
2025-07-27 16:13:10,541 - [BITVAVO] - root - INFO - Swap trade at 2025-05-20 00:00:00+00:00: ETH/USDT,PEPE/USDT -> AAVE/USDT,PEPE/USDT
2025-07-27 16:13:10,541 - [BITVAVO] - root - INFO - Swap trade at 2025-05-31 00:00:00+00:00: AAVE/USDT,PEPE/USDT -> ETH/USDT,AAVE/USDT
2025-07-27 16:13:10,542 - [BITVAVO] - root - INFO - Swap trade at 2025-06-06 00:00:00+00:00: ETH/USDT,AAVE/USDT -> AAVE/USDT,TRX/USDT
2025-07-27 16:13:10,542 - [BITVAVO] - root - INFO - Swap trade at 2025-06-11 00:00:00+00:00: AAVE/USDT,TRX/USDT -> ETH/USDT,AAVE/USDT
2025-07-27 16:13:10,543 - [BITVAVO] - root - INFO - Swap trade at 2025-06-20 00:00:00+00:00: ETH/USDT,AAVE/USDT -> AAVE/USDT,TRX/USDT
2025-07-27 16:13:10,544 - [BITVAVO] - root - INFO - Exit trade at 2025-06-21 00:00:00+00:00 from AAVE/USDT,TRX/USDT
2025-07-27 16:13:10,544 - [BITVAVO] - root - INFO - Entry trade at 2025-07-11 00:00:00+00:00: SUI/USDT,XRP/USDT
2025-07-27 16:13:10,545 - [BITVAVO] - root - INFO - Swap trade at 2025-07-12 00:00:00+00:00: SUI/USDT,XRP/USDT -> ETH/USDT,XRP/USDT
2025-07-27 16:13:10,546 - [BITVAVO] - root - INFO - Swap trade at 2025-07-13 00:00:00+00:00: ETH/USDT,XRP/USDT -> SUI/USDT,XRP/USDT
2025-07-27 16:13:10,546 - [BITVAVO] - root - INFO - Swap trade at 2025-07-14 00:00:00+00:00: SUI/USDT,XRP/USDT -> XRP/USDT,ADA/USDT
2025-07-27 16:13:10,546 - [BITVAVO] - root - INFO - Swap trade at 2025-07-15 00:00:00+00:00: XRP/USDT,ADA/USDT -> SUI/USDT,XRP/USDT
2025-07-27 16:13:10,547 - [BITVAVO] - root - INFO - Swap trade at 2025-07-19 00:00:00+00:00: SUI/USDT,XRP/USDT -> ETH/USDT,XRP/USDT
2025-07-27 16:13:10,547 - [BITVAVO] - root - INFO - Swap trade at 2025-07-21 00:00:00+00:00: ETH/USDT,XRP/USDT -> XRP/USDT,DOGE/USDT
2025-07-27 16:13:10,548 - [BITVAVO] - root - INFO - Swap trade at 2025-07-24 00:00:00+00:00: XRP/USDT,DOGE/USDT -> ETH/USDT,DOGE/USDT
2025-07-27 16:13:10,548 - [BITVAVO] - root - INFO - Total trades: 20 (Entries: 2, Exits: 1, Swaps: 17)
2025-07-27 16:13:10,551 - [BITVAVO] - root - INFO - Strategy execution completed in 0s
2025-07-27 16:13:10,552 - [BITVAVO] - root - INFO - DEBUG: self.elapsed_time = 0.5565016269683838 seconds
2025-07-27 16:13:10,581 - [BITVAVO] - root - INFO - Saved allocation history to allocation_history_weighted_50-50_1d_1d_with_mtpi_no_rebal_independent_imcumbent_2025-02-10.csv
2025-07-27 16:13:10,582 - [BITVAVO] - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-07-27 16:13:10,583 - [BITVAVO] - root - INFO - Assets included in buy-and-hold comparison:
2025-07-27 16:13:10,583 - [BITVAVO] - root - INFO -   ETH/USDT: First available date 2024-10-13
2025-07-27 16:13:10,584 - [BITVAVO] - root - INFO -   BTC/USDT: First available date 2024-10-13
2025-07-27 16:13:10,584 - [BITVAVO] - root - INFO -   SOL/USDT: First available date 2024-10-13
2025-07-27 16:13:10,585 - [BITVAVO] - root - INFO -   SUI/USDT: First available date 2024-10-13
2025-07-27 16:13:10,585 - [BITVAVO] - root - INFO -   XRP/USDT: First available date 2024-10-13
2025-07-27 16:13:10,585 - [BITVAVO] - root - INFO -   AAVE/USDT: First available date 2024-10-13
2025-07-27 16:13:10,586 - [BITVAVO] - root - INFO -   AVAX/USDT: First available date 2024-10-13
2025-07-27 16:13:10,587 - [BITVAVO] - root - INFO -   ADA/USDT: First available date 2024-10-13
2025-07-27 16:13:10,587 - [BITVAVO] - root - INFO -   LINK/USDT: First available date 2024-10-13
2025-07-27 16:13:10,588 - [BITVAVO] - root - INFO -   TRX/USDT: First available date 2024-10-13
2025-07-27 16:13:10,588 - [BITVAVO] - root - INFO -   PEPE/USDT: First available date 2024-10-13
2025-07-27 16:13:10,588 - [BITVAVO] - root - INFO -   DOGE/USDT: First available date 2024-10-13
2025-07-27 16:13:10,589 - [BITVAVO] - root - INFO -   BNB/USDT: First available date 2024-10-13
2025-07-27 16:13:10,590 - [BITVAVO] - root - INFO -   DOT/USDT: First available date 2024-10-13
2025-07-27 16:13:10,591 - [BITVAVO] - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-07-27 16:13:10,594 - [BITVAVO] - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 16:13:10,597 - [BITVAVO] - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 16:13:10,599 - [BITVAVO] - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 16:13:10,602 - [BITVAVO] - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 16:13:10,604 - [BITVAVO] - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 16:13:10,606 - [BITVAVO] - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 16:13:10,609 - [BITVAVO] - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 16:13:10,611 - [BITVAVO] - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 16:13:10,614 - [BITVAVO] - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 16:13:10,616 - [BITVAVO] - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 16:13:10,619 - [BITVAVO] - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 16:13:10,621 - [BITVAVO] - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 16:13:10,624 - [BITVAVO] - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 16:13:10,626 - [BITVAVO] - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 16:13:10,630 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 287 points
2025-07-27 16:13:10,631 - [BITVAVO] - root - INFO - ETH/USDT B&H total return: 40.58%
2025-07-27 16:13:10,634 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 287 points
2025-07-27 16:13:10,634 - [BITVAVO] - root - INFO - BTC/USDT B&H total return: 21.03%
2025-07-27 16:13:10,638 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 287 points
2025-07-27 16:13:10,639 - [BITVAVO] - root - INFO - SOL/USDT B&H total return: -7.78%
2025-07-27 16:13:10,642 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 287 points
2025-07-27 16:13:10,643 - [BITVAVO] - root - INFO - SUI/USDT B&H total return: 29.34%
2025-07-27 16:13:10,647 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 287 points
2025-07-27 16:13:10,647 - [BITVAVO] - root - INFO - XRP/USDT B&H total return: 30.63%
2025-07-27 16:13:10,651 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 287 points
2025-07-27 16:13:10,651 - [BITVAVO] - root - INFO - AAVE/USDT B&H total return: 16.75%
2025-07-27 16:13:10,655 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 287 points
2025-07-27 16:13:10,655 - [BITVAVO] - root - INFO - AVAX/USDT B&H total return: -2.84%
2025-07-27 16:13:10,659 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 287 points
2025-07-27 16:13:10,659 - [BITVAVO] - root - INFO - ADA/USDT B&H total return: 15.12%
2025-07-27 16:13:10,663 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 287 points
2025-07-27 16:13:10,663 - [BITVAVO] - root - INFO - LINK/USDT B&H total return: -2.13%
2025-07-27 16:13:10,667 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 287 points
2025-07-27 16:13:10,667 - [BITVAVO] - root - INFO - TRX/USDT B&H total return: 30.02%
2025-07-27 16:13:10,672 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 287 points
2025-07-27 16:13:10,673 - [BITVAVO] - root - INFO - PEPE/USDT B&H total return: 29.30%
2025-07-27 16:13:10,676 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 287 points
2025-07-27 16:13:10,677 - [BITVAVO] - root - INFO - DOGE/USDT B&H total return: -7.83%
2025-07-27 16:13:10,680 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 287 points
2025-07-27 16:13:10,681 - [BITVAVO] - root - INFO - BNB/USDT B&H total return: 28.23%
2025-07-27 16:13:10,684 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 287 points
2025-07-27 16:13:10,685 - [BITVAVO] - root - INFO - DOT/USDT B&H total return: -14.88%
2025-07-27 16:13:10,687 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-27 16:13:10,699 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-27 16:13:10,811 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-27 16:13:10,827 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-27 16:13:13,483 - [BITVAVO] - root - INFO - Added ETH/USDT buy-and-hold curve with 287 points
2025-07-27 16:13:13,483 - [BITVAVO] - root - INFO - Added BTC/USDT buy-and-hold curve with 287 points
2025-07-27 16:13:13,484 - [BITVAVO] - root - INFO - Added SOL/USDT buy-and-hold curve with 287 points
2025-07-27 16:13:13,484 - [BITVAVO] - root - INFO - Added SUI/USDT buy-and-hold curve with 287 points
2025-07-27 16:13:13,484 - [BITVAVO] - root - INFO - Added XRP/USDT buy-and-hold curve with 287 points
2025-07-27 16:13:13,484 - [BITVAVO] - root - INFO - Added AAVE/USDT buy-and-hold curve with 287 points
2025-07-27 16:13:13,484 - [BITVAVO] - root - INFO - Added AVAX/USDT buy-and-hold curve with 287 points
2025-07-27 16:13:13,484 - [BITVAVO] - root - INFO - Added ADA/USDT buy-and-hold curve with 287 points
2025-07-27 16:13:13,484 - [BITVAVO] - root - INFO - Added LINK/USDT buy-and-hold curve with 287 points
2025-07-27 16:13:13,484 - [BITVAVO] - root - INFO - Added TRX/USDT buy-and-hold curve with 287 points
2025-07-27 16:13:13,484 - [BITVAVO] - root - INFO - Added PEPE/USDT buy-and-hold curve with 287 points
2025-07-27 16:13:13,484 - [BITVAVO] - root - INFO - Added DOGE/USDT buy-and-hold curve with 287 points
2025-07-27 16:13:13,484 - [BITVAVO] - root - INFO - Added BNB/USDT buy-and-hold curve with 287 points
2025-07-27 16:13:13,484 - [BITVAVO] - root - INFO - Added DOT/USDT buy-and-hold curve with 287 points
2025-07-27 16:13:13,484 - [BITVAVO] - root - INFO - Added 14 buy-and-hold curves to results
2025-07-27 16:13:13,485 - [BITVAVO] - root - INFO -   - ETH/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 16:13:13,485 - [BITVAVO] - root - INFO -   - BTC/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 16:13:13,485 - [BITVAVO] - root - INFO -   - SOL/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 16:13:13,485 - [BITVAVO] - root - INFO -   - SUI/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 16:13:13,485 - [BITVAVO] - root - INFO -   - XRP/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 16:13:13,485 - [BITVAVO] - root - INFO -   - AAVE/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 16:13:13,485 - [BITVAVO] - root - INFO -   - AVAX/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 16:13:13,486 - [BITVAVO] - root - INFO -   - ADA/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 16:13:13,486 - [BITVAVO] - root - INFO -   - LINK/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 16:13:13,486 - [BITVAVO] - root - INFO -   - TRX/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 16:13:13,486 - [BITVAVO] - root - INFO -   - PEPE/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 16:13:13,486 - [BITVAVO] - root - INFO -   - DOGE/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 16:13:13,486 - [BITVAVO] - root - INFO -   - BNB/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 16:13:13,486 - [BITVAVO] - root - INFO -   - DOT/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 16:13:13,516 - [BITVAVO] - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-07-27 16:13:13,516 - [BITVAVO] - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-27 16:13:13,519 - [BITVAVO] - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-07-27 16:13:13,519 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-27 16:13:13,530 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-27 16:13:13,530 - [BITVAVO] - root - INFO - Loaded MTPI configuration with 8 enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-27 16:13:13,530 - [BITVAVO] - root - INFO - Using MTPI indicators from config: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-27 16:13:13,530 - [BITVAVO] - root - INFO - Combination method: consensus
2025-07-27 16:13:13,531 - [BITVAVO] - root - INFO - Calculated appropriate limit for 1d timeframe: 216 candles (minimum 180.0 candles needed for 60 length indicator)
2025-07-27 16:13:13,531 - [BITVAVO] - root - INFO - Checking cache for 1 symbols (1d)...
2025-07-27 16:13:13,547 - [BITVAVO] - root - INFO - Loaded 2168 rows of BTC/USDT data from cache (last updated: 2025-07-27)
2025-07-27 16:13:13,548 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 16:13:13,548 - [BITVAVO] - root - INFO - Loaded 2168 rows of BTC/USDT data from cache (after filtering).
2025-07-27 16:13:13,548 - [BITVAVO] - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-27 16:13:13,548 - [BITVAVO] - root - INFO - Fetched BTC data: 2168 candles from 2019-08-20 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 16:13:13,548 - [BITVAVO] - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-07-27 16:13:13,863 - [BITVAVO] - root - INFO - Generated PGO Score signals: {-1: np.int64(996), 0: np.int64(34), 1: np.int64(1138)}
2025-07-27 16:13:13,864 - [BITVAVO] - root - INFO - PGO signal: 1
2025-07-27 16:13:13,864 - [BITVAVO] - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-07-27 16:13:13,960 - [BITVAVO] - root - INFO - Generated BB Score signals: {-1: np.int64(993), 0: np.int64(33), 1: np.int64(1142)}
2025-07-27 16:13:13,961 - [BITVAVO] - root - INFO - Bollinger Bands signal: 1
2025-07-27 16:13:19,500 - [BITVAVO] - root - INFO - Generated DWMA signals using Weighted SD method
2025-07-27 16:13:19,500 - [BITVAVO] - root - INFO - DWMA Score signal: 1
2025-07-27 16:13:20,095 - [BITVAVO] - root - INFO - Generated DEMA Supertrend signals
2025-07-27 16:13:20,096 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(1268), 0: np.int64(1), 1: np.int64(899)}
2025-07-27 16:13:20,096 - [BITVAVO] - root - INFO - Generated DEMA Super Score signals
2025-07-27 16:13:20,096 - [BITVAVO] - root - INFO - DEMA Super Score signal: 1
2025-07-27 16:13:21,701 - [BITVAVO] - root - INFO - Generated DPSD signals
2025-07-27 16:13:21,702 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(1087), 0: np.int64(87), 1: np.int64(994)}
2025-07-27 16:13:21,702 - [BITVAVO] - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-07-27 16:13:21,702 - [BITVAVO] - root - INFO - DPSD Score signal: 1
2025-07-27 16:13:21,793 - [BITVAVO] - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-07-27 16:13:21,793 - [BITVAVO] - root - INFO - Generated AAD Score signals using SMA method
2025-07-27 16:13:21,793 - [BITVAVO] - root - INFO - AAD Score signal: 1
2025-07-27 16:13:22,592 - [BITVAVO] - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-07-27 16:13:22,592 - [BITVAVO] - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-07-27 16:13:22,592 - [BITVAVO] - root - INFO - Dynamic EMA Score signal: 1
2025-07-27 16:13:24,009 - [BITVAVO] - root - INFO - Quantile DEMA Score signal: 1
2025-07-27 16:13:24,009 - [BITVAVO] - root - INFO - Individual signals: {'pgo': 1, 'bollinger_bands': 1, 'dwma_score': 1, 'dema_super_score': 1, 'dpsd_score': 1, 'aad_score': 1, 'dynamic_ema_score': 1, 'quantile_dema_score': 1}
2025-07-27 16:13:24,010 - [BITVAVO] - root - INFO - Combined MTPI signal (consensus): 1
2025-07-27 16:13:24,010 - [BITVAVO] - root - INFO - MTPI Score: 1.000000
2025-07-27 16:13:24,010 - [BITVAVO] - root - INFO - Added current MTPI score to results: 1.000000 (using 1d timeframe)
2025-07-27 16:13:24,015 - [BITVAVO] - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-07-27 16:13:24,016 - [BITVAVO] - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 13.0, 'BTC/USDT': 1.0, 'SOL/USDT': 5.0, 'SUI/USDT': 12.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 9.0, 'LINK/USDT': 8.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 4.0, 'DOGE/USDT': 11.0, 'BNB/USDT': 4.0, 'DOT/USDT': 3.0}
2025-07-27 16:13:24,016 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 13.0, 'BTC/USDT': 1.0, 'SOL/USDT': 5.0, 'SUI/USDT': 12.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 9.0, 'LINK/USDT': 8.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 4.0, 'DOGE/USDT': 11.0, 'BNB/USDT': 4.0, 'DOT/USDT': 3.0}
2025-07-27 16:13:24,016 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-27 16:13:24,016 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-27 16:13:24,016 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 13.0)
2025-07-27 16:13:24,016 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 1.0)
2025-07-27 16:13:24,016 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 5.0)
2025-07-27 16:13:24,016 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 12.0)
2025-07-27 16:13:24,016 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 9.0)
2025-07-27 16:13:24,017 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 0.0)
2025-07-27 16:13:24,017 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 7.0)
2025-07-27 16:13:24,017 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 9.0)
2025-07-27 16:13:24,017 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 8.0)
2025-07-27 16:13:24,017 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 2.0)
2025-07-27 16:13:24,017 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 4.0)
2025-07-27 16:13:24,017 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 11.0)
2025-07-27 16:13:24,017 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 4.0)
2025-07-27 16:13:24,017 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 3.0)
2025-07-27 16:13:24,017 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 13.0, 'BTC/EUR': 1.0, 'SOL/EUR': 5.0, 'SUI/EUR': 12.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 7.0, 'ADA/EUR': 9.0, 'LINK/EUR': 8.0, 'TRX/EUR': 2.0, 'PEPE/EUR': 4.0, 'DOGE/EUR': 11.0, 'BNB/EUR': 4.0, 'DOT/EUR': 3.0}
2025-07-27 16:13:24,017 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-27 16:13:24,017 - [BITVAVO] - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 13.0, 'BTC/EUR': 1.0, 'SOL/EUR': 5.0, 'SUI/EUR': 12.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 7.0, 'ADA/EUR': 9.0, 'LINK/EUR': 8.0, 'TRX/EUR': 2.0, 'PEPE/EUR': 4.0, 'DOGE/EUR': 11.0, 'BNB/EUR': 4.0, 'DOT/EUR': 3.0}
2025-07-27 16:13:24,027 - [BITVAVO] - root - INFO - Saved metrics to new file: Performance_Metrics/metrics_WeightedAllocation_1d_1d_weighted_50-50_assets14_since_20250720_run_20250727_161241.csv
2025-07-27 16:13:24,027 - [BITVAVO] - root - INFO - Saved performance metrics to CSV: Performance_Metrics/metrics_WeightedAllocation_1d_1d_weighted_50-50_assets14_since_20250720_run_20250727_161241.csv
2025-07-27 16:13:24,027 - [BITVAVO] - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-07-27 16:13:24,027 - [BITVAVO] - root - INFO - Results type: <class 'dict'>
2025-07-27 16:13:24,027 - [BITVAVO] - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-07-27 16:13:24,027 - [BITVAVO] - root - INFO - Success flag set to: True
2025-07-27 16:13:24,027 - [BITVAVO] - root - INFO - Message set to: Strategy calculation completed successfully
2025-07-27 16:13:24,027 - [BITVAVO] - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 287 entries
2025-07-27 16:13:24,027 - [BITVAVO] - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-07-27 16:13:24,028 - [BITVAVO] - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 287 entries
2025-07-27 16:13:24,028 - [BITVAVO] - root - INFO -   - mtpi_signals: <class 'pandas.core.series.Series'> with 287 entries
2025-07-27 16:13:24,028 - [BITVAVO] - root - INFO -   - mtpi_score: <class 'float'>
2025-07-27 16:13:24,028 - [BITVAVO] - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 287 entries
2025-07-27 16:13:24,028 - [BITVAVO] - root - INFO -   - performance_metrics: dict with 3 entries
2025-07-27 16:13:24,028 - [BITVAVO] - root - INFO -   - metrics_file: <class 'str'>
2025-07-27 16:13:24,028 - [BITVAVO] - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-07-27 16:13:24,028 - [BITVAVO] - root - INFO -   - success: <class 'bool'>
2025-07-27 16:13:24,028 - [BITVAVO] - root - INFO -   - message: <class 'str'>
2025-07-27 16:13:24,028 - [BITVAVO] - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-07-27 16:13:24,029 - [BITVAVO] - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: ETH/EUR, DOGE/EUR
2025-07-27 16:13:24,030 - [BITVAVO] - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-07-22 00:00:00+00:00    XRP/EUR, DOGE/EUR
2025-07-23 00:00:00+00:00    XRP/EUR, DOGE/EUR
2025-07-24 00:00:00+00:00    ETH/EUR, DOGE/EUR
2025-07-25 00:00:00+00:00    ETH/EUR, DOGE/EUR
2025-07-26 00:00:00+00:00    ETH/EUR, DOGE/EUR
dtype: object
2025-07-27 16:13:24,030 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 13.0, 'BTC/EUR': 1.0, 'SOL/EUR': 5.0, 'SUI/EUR': 12.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 7.0, 'ADA/EUR': 9.0, 'LINK/EUR': 8.0, 'TRX/EUR': 2.0, 'PEPE/EUR': 4.0, 'DOGE/EUR': 11.0, 'BNB/EUR': 4.0, 'DOT/EUR': 3.0}
2025-07-27 16:13:24,030 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-27 16:13:24,030 - [BITVAVO] - root - INFO - [DEBUG] TIE-BREAKING - Strategy: imcumbent
2025-07-27 16:13:24,030 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - find_best_asset_for_day() called with 14 assets
2025-07-27 16:13:24,030 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Input scores: {'ETH/EUR': 13.0, 'BTC/EUR': 1.0, 'SOL/EUR': 5.0, 'SUI/EUR': 12.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 7.0, 'ADA/EUR': 9.0, 'LINK/EUR': 8.0, 'TRX/EUR': 2.0, 'PEPE/EUR': 4.0, 'DOGE/EUR': 11.0, 'BNB/EUR': 4.0, 'DOT/EUR': 3.0}
2025-07-27 16:13:24,030 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-27 16:13:24,030 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Maximum score found: 13.0
2025-07-27 16:13:24,030 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Assets with max score 13.0: ['ETH/EUR']
2025-07-27 16:13:24,031 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - No tie detected, single winner: ETH/EUR
2025-07-27 16:13:24,031 - [BITVAVO] - root - ERROR - [DEBUG] SELECTED BEST ASSET: ETH/EUR (score: 13.0)
2025-07-27 16:13:24,031 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Current day's best asset: ETH/EUR (MTPI signal: 1)
2025-07-27 16:13:24,031 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 13.0
2025-07-27 16:13:24,031 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['ETH/EUR']
2025-07-27 16:13:24,031 - [BITVAVO] - root - ERROR - [DEBUG] NO TIE - UPDATING SELECTION: ETH/EUR, DOGE/EUR -> ETH/EUR
2025-07-27 16:13:24,031 - [BITVAVO] - root - ERROR - [DEBUG] NO TIE - Single winner: ETH/EUR
2025-07-27 16:13:24,062 - [BITVAVO] - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-07-27 16:13:24,063 - [BITVAVO] - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-27 16:13:24,063 - [BITVAVO] - root - INFO - Available assets from config: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-27 16:13:24,063 - [BITVAVO] - root - INFO - Asset columns found in dataframe: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-27 16:13:24,063 - [BITVAVO] - root - INFO - Assets with non-zero allocation: ['ETH/EUR', 'DOGE/EUR']
2025-07-27 16:13:24,063 - [BITVAVO] - root - INFO - All assets sorted by score: [('ETH/EUR', 13.0), ('SUI/EUR', 12.0), ('DOGE/EUR', 11.0), ('XRP/EUR', 9.0), ('ADA/EUR', 9.0), ('LINK/EUR', 8.0), ('AVAX/EUR', 7.0), ('SOL/EUR', 5.0), ('PEPE/EUR', 4.0), ('BNB/EUR', 4.0), ('DOT/EUR', 3.0), ('TRX/EUR', 2.0), ('BTC/EUR', 1.0), ('AAVE/EUR', 0.0)]
2025-07-27 16:13:24,063 - [BITVAVO] - root - INFO - Selected top 2 assets by score: ['ETH/EUR', 'SUI/EUR']
2025-07-27 16:13:24,063 - [BITVAVO] - root - INFO - Updated assets list with top-scoring assets: ['ETH/EUR', 'SUI/EUR']
2025-07-27 16:13:24,063 - [BITVAVO] - root - INFO - Using weighted allocation with configured weights: {'ETH/EUR': 0.5, 'SUI/EUR': 0.5}
2025-07-27 16:13:24,063 - [BITVAVO] - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-07-27 16:13:24,064 - [BITVAVO] - root - INFO - [DEBUG]   - Best asset selected: ETH/EUR
2025-07-27 16:13:24,064 - [BITVAVO] - root - INFO - [DEBUG]   - Assets held: {'ETH/EUR': 0.5, 'SUI/EUR': 0.5}
2025-07-27 16:13:24,064 - [BITVAVO] - root - INFO - [DEBUG]   - MTPI signal: 1
2025-07-27 16:13:24,064 - [BITVAVO] - root - INFO - [DEBUG]   - Use MTPI signal: True
2025-07-27 16:13:24,064 - [BITVAVO] - root - ERROR - 🚨 ? ETH/EUR WAS SELECTED
2025-07-27 16:13:24,064 - [BITVAVO] - root - INFO - Executing multi-asset strategy with 2 assets: {'ETH/EUR': 0.5, 'SUI/EUR': 0.5}
2025-07-27 16:13:24,064 - [BITVAVO] - root - INFO - Passing 14 asset scores to executor for replacement logic
2025-07-27 16:13:24,064 - [BITVAVO] - root - INFO - [DEBUG] PRICE - ETH/EUR: Starting get_current_price
2025-07-27 16:13:24,064 - [BITVAVO] - root - INFO - [DEBUG] PRICE - ETH/EUR: Exchange ID: bitvavo
2025-07-27 16:13:24,065 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Initializing exchange bitvavo
2025-07-27 16:13:24,071 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Exchange initialized successfully
2025-07-27 16:13:24,072 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Exchange markets not loaded, loading now...
2025-07-27 16:13:24,460 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Symbol found after loading markets
2025-07-27 16:13:24,460 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Attempting to fetch ticker...
2025-07-27 16:13:24,527 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker fetched successfully
2025-07-27 16:13:24,527 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker data: {'symbol': 'ETH/EUR', 'timestamp': 1753632801022, 'datetime': '2025-07-27T16:13:21.022Z', 'high': 3270.0, 'low': 3160.0, 'bid': 3257.9, 'bidVolume': 3.07158324, 'ask': 3258.6, 'askVolume': 0.326626, 'vwap': 3224.0928684247974, 'open': 3172.8, 'close': 3257.0, 'last': 3257.0, 'previousClose': None, 'change': 84.2, 'percentage': 2.6538073625819467, 'average': 3214.9, 'baseVolume': 8227.24988238, 'quoteVolume': 26525417.672530115, 'info': {'market': 'ETH-EUR', 'startTimestamp': '1753546401022', 'timestamp': '1753632801022', 'open': '3172.8', 'openTimestamp': '1753546412204', 'high': '3.27E+3', 'low': '3.16E+3', 'last': '3257', 'closeTimestamp': '1753632780859', 'bid': '3257.900', 'bidSize': '3.07158324', 'ask': '3258.600', 'askSize': '0.32662600', 'volume': '8227.24988238', 'volumeQuote': '26525417.672530113'}, 'indexPrice': None, 'markPrice': None}
2025-07-27 16:13:24,527 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Last price: 3257.0
2025-07-27 16:13:24,536 - [BITVAVO] - root - INFO - Loaded market info for 176 trading pairs
2025-07-27 16:13:24,536 - [BITVAVO] - root - INFO - [DEBUG] PRICE - SUI/EUR: Starting get_current_price
2025-07-27 16:13:24,536 - [BITVAVO] - root - INFO - [DEBUG] PRICE - SUI/EUR: Exchange ID: bitvavo
2025-07-27 16:13:24,536 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Using existing exchange instance
2025-07-27 16:13:24,536 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Symbol found in exchange markets
2025-07-27 16:13:24,536 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Attempting to fetch ticker...
2025-07-27 16:13:24,591 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker fetched successfully
2025-07-27 16:13:24,592 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker data: {'symbol': 'SUI/EUR', 'timestamp': 1753632800811, 'datetime': '2025-07-27T16:13:20.811Z', 'high': 3.7709, 'low': 3.5081, 'bid': 3.6333, 'bidVolume': 229.53912243, 'ask': 3.6352, 'askVolume': 361.74177, 'vwap': 3.630090308844542, 'open': 3.5833, 'close': 3.6273, 'last': 3.6273, 'previousClose': None, 'change': 0.044, 'percentage': 1.227918399240923, 'average': 3.6053, 'baseVolume': 3000783.26669436, 'quoteVolume': 10893114.255370062, 'info': {'market': 'SUI-EUR', 'startTimestamp': '1753546400811', 'timestamp': '1753632800811', 'open': '3.5833', 'openTimestamp': '1753546411147', 'high': '3.7709', 'low': '3.5081', 'last': '3.6273', 'closeTimestamp': '1753632765840', 'bid': '3.633300', 'bidSize': '229.53912243', 'ask': '3.635200', 'askSize': '361.74177000', 'volume': '3000783.26669436', 'volumeQuote': '10893114.255370062668'}, 'indexPrice': None, 'markPrice': None}
2025-07-27 16:13:24,592 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Last price: 3.6273
2025-07-27 16:13:24,592 - [BITVAVO] - root - INFO - Original assets with weights: {'ETH/EUR': 0.5, 'SUI/EUR': 0.5}
2025-07-27 16:13:24,592 - [BITVAVO] - root - INFO - Using provided asset scores for replacement logic: {'ETH/EUR': 13.0, 'BTC/EUR': 1.0, 'SOL/EUR': 5.0, 'SUI/EUR': 12.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 7.0, 'ADA/EUR': 9.0, 'LINK/EUR': 8.0, 'TRX/EUR': 2.0, 'PEPE/EUR': 4.0, 'DOGE/EUR': 11.0, 'BNB/EUR': 4.0, 'DOT/EUR': 3.0}
2025-07-27 16:13:24,592 - [BITVAVO] - root - INFO - Top assets by score:
2025-07-27 16:13:24,592 - [BITVAVO] - root - INFO -   ETH/EUR: score=13.0, daily trade limit: OK
2025-07-27 16:13:24,593 - [BITVAVO] - root - INFO -   SUI/EUR: score=12.0, daily trade limit: OK
2025-07-27 16:13:24,593 - [BITVAVO] - root - INFO -   DOGE/EUR: score=11.0, daily trade limit: OK
2025-07-27 16:13:24,593 - [BITVAVO] - root - INFO -   XRP/EUR: score=9.0, daily trade limit: OK
2025-07-27 16:13:24,593 - [BITVAVO] - root - INFO -   ADA/EUR: score=9.0, daily trade limit: OK
2025-07-27 16:13:24,593 - [BITVAVO] - root - INFO -   LINK/EUR: score=8.0, daily trade limit: OK
2025-07-27 16:13:24,593 - [BITVAVO] - root - INFO -   AVAX/EUR: score=7.0, daily trade limit: OK
2025-07-27 16:13:24,593 - [BITVAVO] - root - INFO -   SOL/EUR: score=5.0, daily trade limit: OK
2025-07-27 16:13:24,593 - [BITVAVO] - root - INFO -   PEPE/EUR: score=4.0, daily trade limit: OK
2025-07-27 16:13:24,594 - [BITVAVO] - root - INFO -   BNB/EUR: score=4.0, daily trade limit: OK
2025-07-27 16:13:24,594 - [BITVAVO] - root - INFO - Incremented daily trade counter for ETH/EUR: 1/5
2025-07-27 16:13:24,594 - [BITVAVO] - root - INFO - Incremented daily trade counter for SUI/EUR: 1/5
2025-07-27 16:13:24,594 - [BITVAVO] - root - INFO - No assets were rejected during execution
2025-07-27 16:13:24,594 - [BITVAVO] - root - INFO - Total portfolio value (cached): 100.00000000 EUR
2025-07-27 16:13:24,594 - [BITVAVO] - root - INFO - Using safe available balance: 99.99000000 (99.99% of 100.00000000)
2025-07-27 16:13:24,594 - [BITVAVO] - root - INFO - [DEBUG] PRICE - ETH/EUR: Starting get_current_price
2025-07-27 16:13:24,595 - [BITVAVO] - root - INFO - [DEBUG] PRICE - ETH/EUR: Exchange ID: bitvavo
2025-07-27 16:13:24,595 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Using existing exchange instance
2025-07-27 16:13:24,595 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Symbol found in exchange markets
2025-07-27 16:13:24,595 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Attempting to fetch ticker...
2025-07-27 16:13:24,656 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker fetched successfully
2025-07-27 16:13:24,657 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker data: {'symbol': 'ETH/EUR', 'timestamp': 1753632801022, 'datetime': '2025-07-27T16:13:21.022Z', 'high': 3270.0, 'low': 3160.0, 'bid': 3257.9, 'bidVolume': 3.07158324, 'ask': 3258.6, 'askVolume': 0.326626, 'vwap': 3224.0928684247974, 'open': 3172.8, 'close': 3257.0, 'last': 3257.0, 'previousClose': None, 'change': 84.2, 'percentage': 2.6538073625819467, 'average': 3214.9, 'baseVolume': 8227.24988238, 'quoteVolume': 26525417.672530115, 'info': {'market': 'ETH-EUR', 'startTimestamp': '1753546401022', 'timestamp': '1753632801022', 'open': '3172.8', 'openTimestamp': '1753546412204', 'high': '3.27E+3', 'low': '3.16E+3', 'last': '3257', 'closeTimestamp': '1753632780859', 'bid': '3257.900', 'bidSize': '3.07158324', 'ask': '3258.600', 'askSize': '0.32662600', 'volume': '8227.24988238', 'volumeQuote': '26525417.672530113'}, 'indexPrice': None, 'markPrice': None}
2025-07-27 16:13:24,657 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Last price: 3257.0
2025-07-27 16:13:24,657 - [BITVAVO] - root - INFO - Asset ETH/EUR: weight=0.5000, price=3257.00000000, required=49.86938756, amount=0.01527327
2025-07-27 16:13:24,657 - [BITVAVO] - root - INFO - [DEBUG] PRICE - SUI/EUR: Starting get_current_price
2025-07-27 16:13:24,658 - [BITVAVO] - root - INFO - [DEBUG] PRICE - SUI/EUR: Exchange ID: bitvavo
2025-07-27 16:13:24,658 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Using existing exchange instance
2025-07-27 16:13:24,658 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Symbol found in exchange markets
2025-07-27 16:13:24,658 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Attempting to fetch ticker...
2025-07-27 16:13:24,716 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker fetched successfully
2025-07-27 16:13:24,716 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker data: {'symbol': 'SUI/EUR', 'timestamp': 1753632800811, 'datetime': '2025-07-27T16:13:20.811Z', 'high': 3.7709, 'low': 3.5081, 'bid': 3.6333, 'bidVolume': 229.53912243, 'ask': 3.6352, 'askVolume': 361.74177, 'vwap': 3.630090308844542, 'open': 3.5833, 'close': 3.6273, 'last': 3.6273, 'previousClose': None, 'change': 0.044, 'percentage': 1.227918399240923, 'average': 3.6053, 'baseVolume': 3000783.26669436, 'quoteVolume': 10893114.255370062, 'info': {'market': 'SUI-EUR', 'startTimestamp': '1753546400811', 'timestamp': '1753632800811', 'open': '3.5833', 'openTimestamp': '1753546411147', 'high': '3.7709', 'low': '3.5081', 'last': '3.6273', 'closeTimestamp': '1753632765840', 'bid': '3.633300', 'bidSize': '229.53912243', 'ask': '3.635200', 'askSize': '361.74177000', 'volume': '3000783.26669436', 'volumeQuote': '10893114.255370062668'}, 'indexPrice': None, 'markPrice': None}
2025-07-27 16:13:24,716 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Last price: 3.6273
2025-07-27 16:13:24,717 - [BITVAVO] - root - INFO - Asset SUI/EUR: weight=0.5000, price=3.62730000, required=49.86938756, amount=13.71406418
2025-07-27 16:13:24,717 - [BITVAVO] - root - INFO - Final assets to buy: {'ETH/EUR', 'SUI/EUR'}
2025-07-27 16:13:24,717 - [BITVAVO] - root - INFO - Processing position reductions (sells) first to free up capital...
2025-07-27 16:13:24,717 - [BITVAVO] - root - INFO - Available balance after position reductions: 100.00000000 EUR
2025-07-27 16:13:24,718 - [BITVAVO] - root - INFO - Processing position increases (buys) after capital has been freed...
2025-07-27 16:13:24,718 - [BITVAVO] - root - INFO - Total sale proceeds to distribute: 0.00000000 EUR
2025-07-27 16:13:24,718 - [BITVAVO] - root - INFO - Total weight of new assets: 1.0000
2025-07-27 16:13:24,718 - [BITVAVO] - root - INFO - [DEBUG] PRICE - ETH/EUR: Starting get_current_price
2025-07-27 16:13:24,719 - [BITVAVO] - root - INFO - [DEBUG] PRICE - ETH/EUR: Exchange ID: bitvavo
2025-07-27 16:13:24,719 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Using existing exchange instance
2025-07-27 16:13:24,719 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Symbol found in exchange markets
2025-07-27 16:13:24,719 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Attempting to fetch ticker...
2025-07-27 16:13:24,774 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker fetched successfully
2025-07-27 16:13:24,774 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker data: {'symbol': 'ETH/EUR', 'timestamp': 1753632801022, 'datetime': '2025-07-27T16:13:21.022Z', 'high': 3270.0, 'low': 3160.0, 'bid': 3257.9, 'bidVolume': 3.07158324, 'ask': 3258.6, 'askVolume': 0.326626, 'vwap': 3224.0928684247974, 'open': 3172.8, 'close': 3257.0, 'last': 3257.0, 'previousClose': None, 'change': 84.2, 'percentage': 2.6538073625819467, 'average': 3214.9, 'baseVolume': 8227.24988238, 'quoteVolume': 26525417.672530115, 'info': {'market': 'ETH-EUR', 'startTimestamp': '1753546401022', 'timestamp': '1753632801022', 'open': '3172.8', 'openTimestamp': '1753546412204', 'high': '3.27E+3', 'low': '3.16E+3', 'last': '3257', 'closeTimestamp': '1753632780859', 'bid': '3257.900', 'bidSize': '3.07158324', 'ask': '3258.600', 'askSize': '0.32662600', 'volume': '8227.24988238', 'volumeQuote': '26525417.672530113'}, 'indexPrice': None, 'markPrice': None}
2025-07-27 16:13:24,775 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Last price: 3257.0
2025-07-27 16:13:24,775 - [BITVAVO] - root - INFO - Using weight-based allocation for ETH/EUR: 49.99500000 EUR (50.0% of 99.99000000)
2025-07-27 16:13:24,775 - [BITVAVO] - root - INFO - Buying ETH/EUR: 0.01527327 units at 3257.00000000 (value: 49.74502500)
2025-07-27 16:13:24,776 - [BITVAVO] - root - INFO - Paper trading buy for ETH/EUR: original=0.01527327, adjusted=0.01519690, after_fee=0.01518170
2025-07-27 16:13:24,777 - [BITVAVO] - root - INFO - Saved paper trading history to paper_trading_history.json
2025-07-27 16:13:24,777 - [BITVAVO] - root - INFO - Created paper market buy order: ETH/EUR, amount: 0.015181702049470369, price: 3257.0
2025-07-27 16:13:24,777 - [BITVAVO] - root - INFO - Entered position for ETH/EUR: 0.015273 units ($49.99)
2025-07-27 16:13:24,777 - [BITVAVO] - root - INFO - [DEBUG] PRICE - SUI/EUR: Starting get_current_price
2025-07-27 16:13:24,777 - [BITVAVO] - root - INFO - [DEBUG] PRICE - SUI/EUR: Exchange ID: bitvavo
2025-07-27 16:13:24,777 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Using existing exchange instance
2025-07-27 16:13:24,777 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Symbol found in exchange markets
2025-07-27 16:13:24,777 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Attempting to fetch ticker...
2025-07-27 16:13:24,843 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker fetched successfully
2025-07-27 16:13:24,844 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker data: {'symbol': 'SUI/EUR', 'timestamp': 1753632800811, 'datetime': '2025-07-27T16:13:20.811Z', 'high': 3.7709, 'low': 3.5081, 'bid': 3.6333, 'bidVolume': 229.53912243, 'ask': 3.6352, 'askVolume': 361.74177, 'vwap': 3.630090308844542, 'open': 3.5833, 'close': 3.6273, 'last': 3.6273, 'previousClose': None, 'change': 0.044, 'percentage': 1.227918399240923, 'average': 3.6053, 'baseVolume': 3000783.26669436, 'quoteVolume': 10893114.255370062, 'info': {'market': 'SUI-EUR', 'startTimestamp': '1753546400811', 'timestamp': '1753632800811', 'open': '3.5833', 'openTimestamp': '1753546411147', 'high': '3.7709', 'low': '3.5081', 'last': '3.6273', 'closeTimestamp': '1753632765840', 'bid': '3.633300', 'bidSize': '229.53912243', 'ask': '3.635200', 'askSize': '361.74177000', 'volume': '3000783.26669436', 'volumeQuote': '10893114.255370062668'}, 'indexPrice': None, 'markPrice': None}
2025-07-27 16:13:24,844 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Last price: 3.6273
2025-07-27 16:13:24,844 - [BITVAVO] - root - INFO - Using weight-based allocation for SUI/EUR: 49.99500000 EUR (50.0% of 99.99000000)
2025-07-27 16:13:24,844 - [BITVAVO] - root - INFO - Buying SUI/EUR: 13.71406418 units at 3.62730000 (value: 49.74502500)
2025-07-27 16:13:24,844 - [BITVAVO] - root - INFO - Paper trading buy for SUI/EUR: original=13.71406418, adjusted=13.64549386, after_fee=13.63184837
2025-07-27 16:13:24,846 - [BITVAVO] - root - INFO - Saved paper trading history to paper_trading_history.json
2025-07-27 16:13:24,846 - [BITVAVO] - root - INFO - Created paper market buy order: SUI/EUR, amount: 13.631848365209658, price: 3.6273
2025-07-27 16:13:24,846 - [BITVAVO] - root - INFO - Entered position for SUI/EUR: 13.714064 units ($49.99)
2025-07-27 16:13:24,846 - [BITVAVO] - root - INFO - Updated portfolio with successful trades: {'ETH/EUR': 0.5, 'SUI/EUR': 0.5}
2025-07-27 16:13:24,847 - [BITVAVO] - root - INFO - Trade executed: BUY ETH/EUR, amount=0.01527327, price=3257.00000000, filled=0.01518170
2025-07-27 16:13:24,848 - [BITVAVO] - root - INFO -   Fee: 0.04949630 EUR
2025-07-27 16:13:24,848 - [BITVAVO] - root - INFO - Trade executed: BUY SUI/EUR, amount=13.71406418, price=3.62730000, filled=13.63184837
2025-07-27 16:13:24,849 - [BITVAVO] - root - INFO -   Fee: 0.04949630 EUR
2025-07-27 16:13:24,849 - [BITVAVO] - root - INFO - Multi-asset trade result logged to trade log file
2025-07-27 16:13:24,849 - [BITVAVO] - root - INFO - Multi-asset trades partially executed: 2 of 2 trades successful
2025-07-27 16:13:24,924 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-27 16:13:24,971 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-27 16:13:24,975 - [BITVAVO] - root - INFO - Asset selection logged: 2 assets selected with weighted_custom allocation
2025-07-27 16:13:24,976 - [BITVAVO] - root - INFO - Asset scores (sorted by score):
2025-07-27 16:13:24,976 - [BITVAVO] - root - INFO -   ETH/EUR: score=13.0, status=SELECTED, weight=0.50
2025-07-27 16:13:24,977 - [BITVAVO] - root - INFO -   SUI/EUR: score=12.0, status=SELECTED, weight=0.50
2025-07-27 16:13:24,977 - [BITVAVO] - root - INFO -   DOGE/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-07-27 16:13:24,977 - [BITVAVO] - root - INFO -   XRP/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-27 16:13:24,977 - [BITVAVO] - root - INFO -   ADA/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-27 16:13:24,977 - [BITVAVO] - root - INFO -   LINK/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-07-27 16:13:24,977 - [BITVAVO] - root - INFO -   AVAX/EUR: score=7.0, status=NOT SELECTED, weight=0.00
2025-07-27 16:13:24,977 - [BITVAVO] - root - INFO -   SOL/EUR: score=5.0, status=NOT SELECTED, weight=0.00
2025-07-27 16:13:24,977 - [BITVAVO] - root - INFO -   PEPE/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-07-27 16:13:24,977 - [BITVAVO] - root - INFO -   BNB/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-07-27 16:13:24,977 - [BITVAVO] - root - INFO -   DOT/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-07-27 16:13:24,978 - [BITVAVO] - root - INFO -   TRX/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-07-27 16:13:24,978 - [BITVAVO] - root - INFO -   BTC/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-07-27 16:13:24,978 - [BITVAVO] - root - INFO -   AAVE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-07-27 16:13:24,978 - [BITVAVO] - root - INFO - Asset selection logged with 14 assets scored and 2 assets selected
2025-07-27 16:13:24,979 - [BITVAVO] - root - INFO - Extracted asset scores: {'ETH/EUR': 13.0, 'BTC/EUR': 1.0, 'SOL/EUR': 5.0, 'SUI/EUR': 12.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 7.0, 'ADA/EUR': 9.0, 'LINK/EUR': 8.0, 'TRX/EUR': 2.0, 'PEPE/EUR': 4.0, 'DOGE/EUR': 11.0, 'BNB/EUR': 4.0, 'DOT/EUR': 3.0}
2025-07-27 16:13:24,981 - [BITVAVO] - root - INFO - Top 2 assets by score: ['ETH/EUR', 'SUI/EUR']
2025-07-27 16:13:25,048 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-27 16:13:25,052 - [BITVAVO] - root - INFO - Strategy execution completed successfully in 43.36 seconds
2025-07-27 16:13:25,057 - [BITVAVO] - root - INFO - Saved recovery state to data/state/recovery_state.json
2025-07-27 16:26:35,462 - [BITVAVO] - root - INFO - Received signal 2, shutting down...
2025-07-27 16:26:36,453 - [BITVAVO] - root - INFO - Received signal 2, shutting down...
2025-07-27 16:26:36,456 - [BITVAVO] - root - WARNING - Service is not running
